import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ContactsService } from "./contacts.service";
import { CreateContactDto } from "./dto/create-contact.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { GetContactDto } from "./dto/get-contact.dto";
import { AddLinkedContactDto } from "./dto/add-linked-contact.dto";
import { UpdateDndDto } from "./dto/update-dnd.dto";
import { TagsDto } from "./dto/tags.dto";
import { GetSearchContactDto } from "./dto/search.dto";
import { CreateContactCommentDto } from "./dto/create-contact-comment.dto";
import { CreateNewActionDto } from "./dto/create-new-action.dto";
import { UpdateContactActivityDto } from "./dto/update-contact-activity.dto";
import { LostAndUnLostContactDto } from "./dto/lost-contact.dto";
import { UpdateLeadStatusDto } from "src/lead/dto/update-lead-status.dto";
import { StatusEnum } from "src/time-card/enum/status.enum";

@ApiTags("Contacts")
@Controller({ path: "contact", version: "1" })
@Auth()
@ApiBearerAuth()
export class ContactsController {
    constructor(private readonly contactsService: ContactsService) {}

    @Post()
    @ApiOperation({ summary: "Create a new contact" })
    async create(@Body() createContactDto: CreateContactDto, @GetUser() user: JwtUserPayload) {
        return this.contactsService.create(createContactDto, user);
    }

    @Get()
    @ApiOperation({ summary: "Get all contacts" })
    async findAll(@Query() getContactDto: GetContactDto, @GetUser() user: JwtUserPayload) {
        return this.contactsService.findAll(getContactDto, user);
    }

    @Get("id/:id")
    @ApiOperation({ summary: "Get a contact by id" })
    async findOne(@Param("id") id: string, @GetUser() user: JwtUserPayload) {
        return this.contactsService.findOne(id, user);
    }

    @Put("id/:id")
    @ApiOperation({ summary: "Update a contact" })
    async update(
        @Param("id") id: string,
        @Body() updateContactDto: CreateContactDto,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.update(id, updateContactDto, user);
    }

    @Delete("id/:id")
    @ApiOperation({ summary: "Delete a contact" })
    async remove(@Param("id") id: string, @GetUser() user: JwtUserPayload) {
        return this.contactsService.remove(id, user);
    }

    @Delete("multiple")
    @ApiOperation({ summary: "Delete multiple contacts" })
    async removeMultipleContacts(@Body() ids: string[], @GetUser() user: JwtUserPayload) {
        return this.contactsService.removeMultipleContacts(ids, user);
    }

    @Delete("permanently-delete/:id")
    @ApiOperation({ summary: "Permanently delete a contact" })
    async permDelete(@Param("id") id: string, @GetUser() user: JwtUserPayload) {
        return this.contactsService.permDelete(id, user);
    }

    @Put("restore/:id")
    @ApiOperation({ summary: "Restore a contact" })
    async restore(@Param("id") id: string, @GetUser() user: JwtUserPayload) {
        return this.contactsService.restore(id, user);
    }

    @Put("multi-restore")
    @ApiOperation({ summary: "Restore multiple contacts" })
    async restoreMultipleContacts(@Body() ids: string[], @GetUser() user: JwtUserPayload) {
        return this.contactsService.restoreMultipleContacts(ids, user);
    }

    @Put("add-linked-contact/:id")
    @ApiOperation({ summary: "Add a linked contact" })
    async addLinkedContact(
        @Param("id") id: string,
        @Body() linkedContactDto: AddLinkedContactDto[],
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.addLinkedContact(id, linkedContactDto, user);
    }

    @Delete("remove-linked-contact/:id/:linkedContactId")
    @ApiOperation({ summary: "Remove a linked contact" })
    async removeLinkedContact(
        @Param("id") id: string,
        @Param("linkedContactId") linkedContactId: string,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.removeLinkedContact(id, linkedContactId, user);
    }

    @Get("search")
    @ApiOperation({ summary: "Search for contacts" })
    async searchContacts(@Query() dto: GetSearchContactDto, @GetUser() user: JwtUserPayload) {
        return this.contactsService.searchContacts(dto, user);
    }

    @Get("get-linked-contact/:id")
    @ApiOperation({ summary: "Get linked contacts" })
    async getLinkedContact(@Param("id") id: string, @GetUser() user: JwtUserPayload) {
        return this.contactsService.getLinkedContact(id, user);
    }

    @Put("update-dnd/:id")
    @ApiOperation({ summary: "Update DND settings" })
    async updateDnd(
        @Param("id") id: string,
        @Body() updateDndDto: UpdateDndDto,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.updateDnd(id, updateDndDto, user);
    }

    @Put("add-tags")
    @ApiOperation({ summary: "Add tags to multiple contacts" })
    async addTagsInMultipleContacts(@Body() addTagsDto: TagsDto, @GetUser() user: JwtUserPayload) {
        return this.contactsService.addTagsInMultipleContacts(addTagsDto.ids, addTagsDto.tags, user);
    }

    @Put("remove-tags")
    @ApiOperation({ summary: "Remove tags from multiple contacts" })
    async removeTagsInMultipleContacts(@Body() addTagsDto: TagsDto, @GetUser() user: JwtUserPayload) {
        return this.contactsService.removeTagsInMultipleContacts(addTagsDto.ids, addTagsDto.tags, user);
    }

    @Get("opportunities/:contactId")
    @ApiOperation({ summary: "Get contact opportunities" })
    async getContactOpportunities(@Param("contactId") contactId: string, @GetUser() user: JwtUserPayload) {
        return this.contactsService.getContactOpportunities(contactId, user);
    }

    @Put("migrate-opp/fromContact/:fromContactId/toContact/:toContactId")
    @ApiOperation({ summary: "Migrate contact and opportunities" })
    async migrateContactAndOpp(
        @Param("fromContactId") fromContactId: string,
        @Param("toContactId") toContactId: string,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.migrateContactAndOpp(fromContactId, toContactId, user);
    }

    // comments
    @Post("comment/:id")
    @ApiOperation({ summary: "Add comment to contact" })
    async addComment(
        @Param("id") id: string,
        @Body() createContactCommentDto: CreateContactCommentDto,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.addComment(id, createContactCommentDto);
    }

    @Get("comment/:id")
    @ApiOperation({ summary: "Get comments on contact" })
    async getComments(@Param("id") id: string) {
        return this.contactsService.getComments(id);
    }

    @Put("comment/:id/:commentId")
    @ApiOperation({ summary: "Update comment on contact" })
    async updateComment(
        @Param("id") id: string,
        @Param("commentId") commentId: string,
        @Body() body: string,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.updateComment(user, id, commentId, body);
    }

    @Delete("comment/:id/:commentId")
    @ApiOperation({ summary: "Delete comment on contact" })
    async deleteComment(
        @Param("id") id: string,
        @Param("commentId") commentId: string,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.deleteComment(id, commentId, user.memberId);
    }

    @Put("status/:id")
    @ApiOperation({ summary: "Update lead status" })
    async updateLeadStatus(
        @Param("id") id: string,
        @Body() status: StatusEnum,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.updateContactStatus(id, status);
    }

    // actions
    @Post("action/:id")
    @ApiOperation({ summary: "Create new action for contact" })
    async createNewAction(
        @Param("id") id: string,
        @Body() createNewActionDto: CreateNewActionDto,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.createNewAction(user.companyId, user.memberId, id, createNewActionDto);
    }

    @Put("action/complete/:id")
    @ApiOperation({ summary: "Complete action for contact" })
    async completeAction(
        @Param("id") id: string,
        @Body() createNewActionDto: CreateNewActionDto,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.completeAction(user.companyId, user.memberId, id, createNewActionDto);
    }

    @Get("actions/:id")
    @ApiOperation({ summary: "Get actions for contact" })
    async getActions(@Param("id") id: string) {
        return this.contactsService.getActions(id);
    }

    // activity

    @Get("activity/:id")
    @ApiOperation({ summary: "Get contact activity" })
    async getContactActivity(@Param("id") id: string) {
        return this.contactsService.getContactActivity(id);
    }

    @Put("activity/:id")
    @ApiOperation({ summary: "Update contact activity" })
    async updateContactActivity(
        @Param("id") id: string,
        @Body() updateContactActivityDto: UpdateContactActivityDto,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.contactsService.updateContactActivity(
            user.companyId,
            user.memberId,
            updateContactActivityDto,
        );
    }

    // Lead
    // @Get("lead/status/:status/deleted/:deleted")
    // @ApiOperation({ summary: "Get all leads" })
    // async getLeads(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("status") status: string,
    //     @Param("deleted") deleted: boolean,
    // ) {
    //     return this.contactsService.getContactOfTypeLeads(status, deleted, user);
    // }

    // @Put("lost-lead/:id")
    // @ApiOperation({ summary: "Lost contact" })
    // async lostContact(
    //     @Param("id") id: string,
    //     @Body() lostContactDto: LostAndUnLostContactDto,
    //     @GetUser() user: JwtUserPayload,
    // ) {
    //     return this.contactsService.lostContact(id, user.memberId, lostContactDto);
    // }

    // @Put("unlost-lead/:id")
    // @ApiOperation({ summary: "UnLost contact" })
    // async unLostContact(
    //     @Param("id") id: string,
    //     @Body() unLostContactDto: LostAndUnLostContactDto,
    //     @GetUser() user: JwtUserPayload,
    // ) {
    //     return this.contactsService.unLostContact(id, user.memberId, unLostContactDto);
    // }

    // @Put("lead-stage/:id/stage/:stageId")
    // @ApiOperation({ summary: "Update lead stage" })
    // async updateLeadStage(
    //     @Param("id") id: string,
    //     @Param("stageId") stageId: string,
    //     @GetUser() user: JwtUserPayload,
    // ) {
    //     return this.contactsService.updateLeadStage(id, stageId);
    // }

    @Get("leads/:contactId")
    @ApiOperation({ summary: "Get lead by contact id" })
    async getLeadByContactId(@Param("contactId") contactId: string, @GetUser() user: JwtUserPayload) {
        return this.contactsService.getLeadByContactId(contactId, user.companyId);
    }
}
