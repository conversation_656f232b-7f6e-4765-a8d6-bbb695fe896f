import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type ActivityLogDocument = ActivityLog & Document;

@Schema({ timestamps: true, id: false, strict: false, collection: "ActivityLog" })
export class ActivityLog {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp() // opportunity id, project id, contact id
    moduleId?: string;

    @Prop({ required: true, type: String, enum: ["opportunity", "contact"] })
    moduleType: string;

    @Prop({ required: true, type: Array })
    activities: any[];

    @Prop()
    createdAt?: Date;

    @Prop()
    modifiedAt?: Date;
}

export const ActivityLogSchema = SchemaFactory.createForClass(ActivityLog);

// adding indexes for performance
ActivityLogSchema.index({ companyId: 1, moduleId: 1 });
ActivityLogSchema.index({ companyId: 1, moduleId: 1, moduleType: 1 });
