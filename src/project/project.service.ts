import {
    BadRequestException,
    ConflictException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Model, Connection } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { CompanySettingDocument } from "src/company/schema/company-setting.schema";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { OpportunityStatusEnum } from "src/opportunity/enum/opportunityStatus.enum";
import {
    addDefaultProjectType,
    defaultCategory,
    defaultInputSetting,
    defaultMaterial,
    defaultOptions,
    defaultPackages,
    defaultSubCategory,
    defaultTasks,
    defaultTaxJurisdictions,
    defaultUnitSetting,
} from "src/shared/constants";
import { averagePitch, getPitchMod, profitScoreCalc, roundTo2, startOfDate } from "src/shared/helpers/logics";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateCategoryDto } from "./dto/create-category.dto";
import { CreateInputDto } from "./dto/create-input.dto";
import { CreateMaterialDto } from "./dto/create-material.dto";
import { CreateOrderDto } from "./dto/create-order.dto";
import { CreatePackageDto } from "./dto/create-package.dto";
import { CreatePriceDto } from "./dto/create-price.dto";
import { CreateProjectTypeDto } from "./dto/create-project-type.dto";
import { CreateProjectDto } from "./dto/create-project.dto";
import { CreateSubCategoryDto } from "./dto/create-sub-category.dto";
import { CreateTaskDto } from "./dto/create-task.dto";
import { CreateTaxJurisdictionDto } from "./dto/create-tax-jurisdiction.dto";
import { CreateUnitDto } from "./dto/create-unit.dto";
import { DeleteCategoryDto } from "./dto/delete-category.dto";
import { DeleteInputDto } from "./dto/delete-input.dto";
import { DeleteMaterialDto } from "./dto/delete-material.dto";
import { DeletePackageDto } from "./dto/delete-package.dto";
import { DeleteProjectTypeDto } from "./dto/delete-project-type.dto";
import { DeleteProjectDto } from "./dto/delete-project.dto";
import { DeleteSubCategoryDto } from "./dto/delete-sub-category.dto";
import { DeleteTaskDto } from "./dto/delete-task.dto";
import { DeleteTaxJurisdictionDto } from "./dto/delete-tax-jurisdiction.dto";
import { DeleteUnitDto } from "./dto/delete-unit.dto";
import { RestoreCategoryDto } from "./dto/restore-category.dto";
import { RestoreInputDto } from "./dto/restore-input.dto";
import { RestoreMaterialDto } from "./dto/restore-material.dto";
import { RestorePackageDto } from "./dto/restore-package.dto";
import { RestoreProjectTypeDto } from "./dto/restore-project-type-dto";
import { RestoreSubCategoryDto } from "./dto/restore-sub-category.schema";
import { RestoreTaskDto } from "./dto/restore-task.dto";
import { RestoreTaxJurisdictionDto } from "./dto/restore-tax-jurisdiction.dto";
import { RestoreUnitDto } from "./dto/restore-unit.dto";
import { UpdateCategoryDto } from "./dto/update-category.dto";
import { UpdateInputDto } from "./dto/update-input.dto";
import { UpdateMaterialDto } from "./dto/update-material.dto";
import { UpdateOppClientDto } from "./dto/update-opp-client.dto";
import { UpdatePackageDto } from "./dto/update-package.dto";
import { UpdateProjectTypeDto } from "./dto/update-project-type.dto";
import { UpdateSubCategoryDto } from "./dto/update-sub-category.schema";
import { UpdateTaskDto } from "./dto/update-task.dto";
import { UpdateTaxJurisdictionDto } from "./dto/update-tax-jurisdiction.dto";
import { UpdateUnitDto } from "./dto/update-unit.dto";
import { CategoryDocument } from "./schema/category.schema";
import { CrewPositionDocument } from "./schema/crew-position.schema";
import { InputDocument } from "./schema/input.schema";
import { MaterialDocument } from "./schema/material.schema";
import { OrderDocument } from "./schema/order.schema";
import { PackageDocument } from "./schema/package.schema";
import { PriceDocument } from "./schema/price-schema";
import { ProjectTypeDocument } from "./schema/project-type.schema";
import { ProjectDocument } from "./schema/project.schema";
import { SubCategoryDocument } from "./schema/sub-category.schema";
import { TaskDocument } from "./schema/task.schema";
import { TaxJurisdictionDocument } from "./schema/tax-jurisdiction.schema";
import { UnitDocument } from "./schema/unit.schema";
import { UpdateOrderDto } from "./dto/update-order.dto";
import { DeleteOrderDto } from "./dto/delete-order.dto";
import { RestoreOrderDto } from "./dto/restore-order.dto";
import { GetInputsDto } from "./dto/get-inputs.dto";
import { GetTaskDto } from "./dto/get-task-dto";
import { RestoreProjectDto } from "./dto/restore-project.dto";
import { GetOrderDto } from "./dto/get-order.dto";
import { DeletePriceDto } from "./dto/delete-price.dto";
import { RestorePriceDto } from "./dto/restore-price.dto";
import { ProjectInputsDto } from "./dto/project-input.dto";
import { AddPriceDto } from "./dto/add-price.dto";
import { UpdatePriceDto } from "./dto/update-price.dto";
import { GetMatDto } from "./dto/get-mat.dto";
import { CreateOptionDto } from "./dto/create-options.dto";
import { OptionsDocument } from "./schema/options.schema";
import { UpdateOptionDto } from "./dto/update-options.dto";
import { DeleteRestoreDto } from "src/shared/dto/delete-retore.dto";
import { WorkTaskDocument } from "src/work-task/schema/work-task.schema";
import { CustomProjectService } from "src/custom-project/custom-project.service";
import { GetPackageOptionDto } from "./dto/get-package-option.dto";
import { UpdateSequenceDto } from "src/crm/dto/updateSequence.dto";
import { UpdateMaterialOrderDto } from "./dto/update-material-order.dto";
import { AddMaterialToOrderDto } from "./dto/add-material-order.dto";
import { OpportunityService } from "src/opportunity/opportunity.service";
import { ContactDocument } from "src/contacts/schema/contact.schema";
import { ContactTypeEnum } from "src/contacts/enum/contact.enum";

@Injectable()
export class ProjectService {
    constructor(
        private readonly opportunityService: OpportunityService,
        private readonly customProjectService: CustomProjectService,
        @InjectConnection() private readonly connection: Connection,
        @InjectModel("TaxJurisdiction") private readonly taxModel: Model<TaxJurisdictionDocument>,
        @InjectModel("Task") private readonly taskModel: Model<TaskDocument>,
        @InjectModel("Unit") private readonly unitModel: Model<UnitDocument>,
        @InjectModel("Contact") private readonly contactModel: Model<ContactDocument>,
        @InjectModel("Order") private readonly orderModel: Model<OrderDocument>,
        @InjectModel("Price") private readonly priceModel: Model<PriceDocument>,
        @InjectModel("Input") private readonly inputModel: Model<InputDocument>,
        @InjectModel("Package") private readonly packageModel: Model<PackageDocument>,
        @InjectModel("Project") private readonly projectModel: Model<ProjectDocument>,
        @InjectModel("Material") private readonly materialModel: Model<MaterialDocument>,
        @InjectModel("Category") private readonly categoryModel: Model<CategoryDocument>,
        @InjectModel("ProjectType") private readonly projectTypeModel: Model<ProjectTypeDocument>,
        @InjectModel("SubCategory") private readonly subCategoryModel: Model<SubCategoryDocument>,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
        @InjectModel("CrewPosition") private readonly crewPositionModel: Model<CrewPositionDocument>,
        @InjectModel("WorkTask") private readonly workTaskModel: Model<WorkTaskDocument>,
        @InjectModel("CompanySetting") private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("Options") private readonly optionModel: Model<OptionsDocument>,
    ) {}

    /**
     * Create a new unit.
     * @param {string} userId - The ID of the user.
     * @param {CreateUnitDto} createUnitDto - The DTO containing unit data.
     * @returns {CreatedResponse} A promise that resolves to a CreatedResponse indicating the success of the operation.
     * @throws {HttpException} Throws a HttpException with a BAD_REQUEST status code if the unit already exists.
     * @throws {InternalServerErrorException} Throws an InternalServerErrorException if an error occurs during the operation.
     */
    async createUnit(companyId: string, createUnitDto: CreateUnitDto) {
        try {
            const unit = await this.unitModel
                .exists({
                    name: createUnitDto.name,
                    companyId,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (unit) throw new HttpException("Unit already exists", HttpStatus.BAD_REQUEST);
            const createdUnit = new this.unitModel({
                companyId,
                ...createUnitDto,
            });
            await createdUnit.save();
            return new CreatedResponse({ unit: createdUnit, message: "Unit created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Deletes a unit.
     * @param {string} userId - The ID of the user.
     * @param {DeleteUnitDto} deleteUnitDto - The DTO containing the unit ID to delete.
     * @returns {Promise<NoContentResponse>} - A promise that resolves to a NoContentResponse indicating the unit deletion was successful.
     * @throws {HttpException|InternalServerErrorException} - Throws an HttpException if an error occurs during deletion, or an InternalServerErrorException if the error is not of HttpException type.
     */
    async deleteUnit(userId: string, deleteUnitDto: DeleteUnitDto) {
        try {
            const result = await this.unitModel.updateOne(
                { _id: deleteUnitDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Unit deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Deletes a unit permanently.
     * @param userId - The ID of the user performing the operation.
     * @param deleteUnitDto - The DTO containing the unit ID and company ID to be deleted.
     * @returns A NoContentResponse indicating that the unit has been deleted permanently.
     * @throws If an error occurs during the operation, it will be thrown as an InternalServerErrorException.
     */
    async permDeleteUnit(companyId: string, deleteUnitDto: DeleteUnitDto) {
        try {
            await this.unitModel.deleteOne({
                _id: deleteUnitDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "Unit deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Restores a deleted unit.
     * @param userId - The ID of the user performing the restore operation.
     * @param restoreUnitDto - The data required to restore a unit, including the unit ID.
     * @returns A response indicating the success of the restore operation.
     * @throws HttpException if an error occurs during the restore operation.
     * @throws InternalServerErrorException if an unexpected error occurs.
     */
    async restoreUnit(userId: string, restoreUnitDto: RestoreUnitDto) {
        try {
            const result = await this.unitModel.updateOne(
                { _id: restoreUnitDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Unit restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Updates a unit based on the provided information.
     * @param userId - The ID of the user performing the update.
     * @param updateUnitDto - The data required to update the unit.
     * @returns A response indicating the success of the update operation.
     * @throws InternalServerErrorException if an unexpected error occurs during the update.
     */
    async updateUnit(userId: string, updateUnitDto: UpdateUnitDto) {
        try {
            const result = await this.unitModel.updateOne(
                { _id: updateUnitDto.unitId, deleted: false },
                {
                    $set: { ...updateUnitDto },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Unit updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Retrieves units based on the provided parameters.
     * @param userId - The ID of the user.
     * @param companyId - The ID of the company.
     * @param deleted - Indicates whether the units should include deleted ones.
     * @param paginationRequestDto - The DTO object containing pagination parameters.
     * @returns A response object containing the retrieved units.
     * @throws {InternalServerErrorException} If an internal server error occurs.
     */
    async getUnit(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const unit = await this.unitModel.find({ companyId, deleted }).skip(offset).limit(limit);
            return new OkResponse({ unit });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves a unit by its ID.
     *@param {string} userId - The ID of the user.
     *@param {string} companyId - The ID of the company.
     *@param {string} unitId - The ID of the unit to retrieve.
     *@param {boolean} deleted - Indicates whether the unit is deleted.
     *@returns {Promise<OkResponse>} The unit wrapped in an OkResponse object.
     *@throws {HttpException} If an HttpException is encountered during the operation.
     *@throws {InternalServerErrorException} If an error occurs during the operation.
     */
    async getUnitById(userId: string, companyId: string, unitId: string, deleted: boolean) {
        try {
            const unit = await this.unitModel.findOne({
                _id: unitId,
                companyId,
                deleted,
            });
            return new OkResponse({ unit });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Adds a unit setting for a company and member.
     *@param {string} companyId - The ID of the company.
     *@param {string} memberId - The ID of the member.
     *@param {UnitUUID} unitUUID - The UUID of the unit.
     *@returns {Promise<CreatedResponse>} The response indicating the unit setting creation.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async addUnitSetting(companyId: string, memberId: string, unitUUID) {
        try {
            const defaultValues = defaultUnitSetting(unitUUID);
            const unitData = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy: memberId,
                deleted: false,
            }));
            await this.unitModel.insertMany(unitData);
            return new CreatedResponse({
                message: "Unit Setting created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Creates an input with the provided data.
     *@param {string} userId - The ID of the user.
     *@param {CreateInputDto} createInputDto - The data for creating the input.
     *@returns {Promise<CreatedResponse>} The response indicating the input creation.
     *@throws {HttpException} If the input already exists.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async createInput(companyId: string, createInputDto: CreateInputDto) {
        try {
            const input = await this.inputModel
                .exists({
                    name: createInputDto.name,
                    companyId,
                    projectType: createInputDto.projectType,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (input) throw new HttpException("Input already exists", HttpStatus.BAD_REQUEST);
            const createdInput = new this.inputModel({
                companyId,
                ...createInputDto,
                isNew: true,
            });
            await createdInput.save();
            return new CreatedResponse({ input: createdInput, message: "Input created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Deletes an input.
     *@param {string} userId - The ID of the user.
     *@param {DeleteInputDto} deleteInputDto - The DTO containing the ID of the input to delete.
     *@returns {Promise<NoContentResponse>} The response indicating the successful deletion.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async deleteInput(companyId: string, deleteInputDto: DeleteInputDto) {
        try {
            const tsk = await this.taskModel.countDocuments({
                companyId,
                input: {
                    $elemMatch: {
                        input: deleteInputDto.id,
                    },
                },
            });

            let errorMessage = "";
            if (tsk > 0)
                errorMessage += `The Input you're trying to delete is currently in use in one or more tasks and cannot be deleted.`;

            const projectUsage = await this.projectModel.aggregate([
                {
                    $match: {
                        companyId,
                        projectInputs: { $elemMatch: { _id: deleteInputDto.id, value: { $ne: 0 } } },
                    },
                },
                {
                    $lookup: {
                        from: "Opportunity",
                        localField: "oppId",
                        foreignField: "_id",
                        as: "opportunityDetails",
                        pipeline: [
                            {
                                $project: {
                                    PO: 1,
                                    num: 1,
                                },
                            },
                        ],
                    },
                },
                // Unwind the opportunityDetails to flatten the array
                {
                    $unwind: {
                        path: "$opportunityDetails",
                        preserveNullAndEmptyArrays: true, // If no match, keep project with null PO
                    },
                },
                {
                    $project: {
                        _id: 1,
                        name: 1,
                        opportunityDetails: 1,
                        oppId: 1,
                        PO: "$opportunityDetails.PO",
                        num: "$opportunityDetails.num",
                    },
                },
            ]);

            if (projectUsage.length > 0) {
                const poDetails = [...new Set(projectUsage.map((project) => `${project.PO}-${project.num}`))];
                let poMessage = poDetails.slice(0, 3).join(", ");
                if (poDetails.length > 3) {
                    poMessage += ` and many more...`;
                }

                errorMessage += ` The Input you're trying to delete has been used in ${poDetails.length} project(s). PO#: ${poMessage}`;
            }
            if (errorMessage) {
                throw new ConflictException(errorMessage);
            }

            const result = await this.inputModel.updateOne(
                { _id: deleteInputDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                throw new InternalServerErrorException({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Input deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Permanently deletes an input.
     *@param {string} userId - The ID of the user.
     *@param {DeleteInputDto} deleteInputDto - The DTO containing the ID and company ID of the input to delete.
     *@returns {Promise<NoContentResponse>} The response indicating the successful deletion.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async permDeleteInput(companyId: string, deleteInputDto: DeleteInputDto) {
        try {
            await this.inputModel.deleteOne({
                _id: deleteInputDto.id,
                companyId,
                delete: true,
            });
            return new NoContentResponse({ message: "Input deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Restores a deleted input.
     *@param {string} userId - The ID of the user.
     *@param {RestoreInputDto} restoreInputDto - The input restoration data.
     *@returns {Promise<OkResponse>} The response indicating the successful input restoration.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async restoreInput(userId: string, restoreInputDto: RestoreInputDto) {
        try {
            const result = await this.inputModel.updateOne(
                { _id: restoreInputDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Input restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Updates an input based on the provided input ID and DTO.
     *@param {string} userId - The ID of the user.
     *@param {UpdateInputDto} updateInputDto - The DTO containing the updated input data.
     *@returns {Promise<OkResponse>} The response indicating the successful input update.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async updateInput(userId: string, updateInputDto: UpdateInputDto) {
        try {
            const query = {
                _id: { $ne: updateInputDto.inputId },
                name: updateInputDto.name,
                projectType: updateInputDto.projectType,
                deleted: false,
            };

            const existingInput = await this.inputModel
                .findOne(query)
                .collation({ locale: "en", strength: 2 })
                .exec();

            if (existingInput) {
                throw new HttpException(
                    "Input with same name and project type already exists",
                    HttpStatus.BAD_REQUEST,
                );
            }

            const result = await this.inputModel.updateOne(
                { _id: updateInputDto.inputId, deleted: false },
                {
                    $set: { ...updateInputDto },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Input updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Adds an input setting for a company and member.
     *@param {string} companyId - The ID of the company.
     *@param {string} memberId - The ID of the member.
     *@param {InputUUID} inputUUID - The UUID of the input.
     *@param {ProjectTypeUUID} projectTypeUUID - The UUID of the project type.
     *@param {UnitUUID} unitUUID - The UUID of the unit.
     *@returns {Promise<CreatedResponse>} The response indicating the input setting creation.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async addInputSetting(companyId: string, memberId: string, inputUUID, projectTypeUUID, unitUUID) {
        try {
            const defaultValues = defaultInputSetting(inputUUID, projectTypeUUID, unitUUID);
            const inputData = await defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy: memberId,
                deleted: false,
            }));
            await this.inputModel.insertMany(inputData);
            return new CreatedResponse({
                message: "Input Setting created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves inputs based on the specified filters.
     *@param {string} userId - The ID of the user.
     *@param {string} companyId - The ID of the company.
     *@param {boolean} deleted - Indicates whether inputs are deleted or not.
     *@param {GetInputsDto} getInputsDto - The DTO containing the filters for retrieving inputs.
     *@returns {Promise<OkResponse>} The response containing the retrieved inputs.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async getInput(userId: string, companyId: string, deleted: boolean, getInputsDto: GetInputsDto) {
        try {
            const { unit, projectType, isNew } = getInputsDto;
            const limit = getInputsDto.limit || 10;
            const offset = limit * (getInputsDto.skip || 0);
            const query = {
                companyId,
                deleted,
                ...(unit && { unit }),
                ...(projectType && { projectType }),
                ...(isNew && { isNew }),
            };

            const input = await this.inputModel.aggregate([
                { $match: query },
                { $sort: { orderNumber: 1 } },
                { $skip: offset },
                { $limit: limit },
                {
                    $lookup: {
                        from: "Task",
                        let: { inputId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    companyId,
                                    $expr: {
                                        $in: ["$$inputId", "$input.input"],
                                    },
                                },
                            },
                            { $project: { _id: 1, name: 1, active: 1, deleted: 1 } },
                        ],
                        as: "projectTasks",
                    },
                },
            ]);
            return new OkResponse({ input });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves an input by its ID.
     *@param {string} userId - The ID of the user.
     *@param {string} companyId - The ID of the company.
     *@param {string} inputId - The ID of the input.
     *@param {boolean} deleted - Indicates whether the input is deleted.
     *@returns {Promise<OkResponse>} The response containing the retrieved input.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async getInputById(userId: string, companyId: string, inputId: string, deleted: boolean) {
        try {
            const input = await this.inputModel.findOne({
                _id: inputId,
                companyId,
                deleted,
            });
            return new OkResponse({ input });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Creates a category.
     *@param {string} userId - The ID of the user.
     *@param {CreateCategoryDto} createCategoryDto - The DTO containing the category data.
     *@returns {Promise<CreatedResponse>} The response indicating the category creation.
     *@throws {HttpException} If the category already exists.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async createCategory(companyId: string, createCategoryDto: CreateCategoryDto) {
        try {
            const category = await this.categoryModel
                .exists({
                    name: createCategoryDto.name,
                    companyId,
                    deleted: false,
                })
                .exec();
            if (category) throw new HttpException("Category already exists", HttpStatus.BAD_REQUEST);
            const createdCategory = new this.categoryModel({
                companyId,
                ...createCategoryDto,
            });
            await createdCategory.save();
            return new CreatedResponse({ message: "Category created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Delete a category and its sub-categories.
     *@param {string} userId - The ID of the user performing the deletion.
     *@param {DeleteCategoryDto} deleteCategoryDto - The DTO containing deletion data.
     *@throws {HttpException} If the category or sub-categories are not found or if an error occurs.
     *@returns {NoContentResponse} The response indicating successful deletion.
     */
    async deleteCategory(companyId: string, deleteCategoryDto: DeleteCategoryDto) {
        try {
            const result = await this.categoryModel.updateOne(
                { _id: deleteCategoryDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            } else {
                //fetching all sub-category and deleting them
                await this.subCategoryModel.updateMany(
                    {
                        companyId,
                        categoryId: deleteCategoryDto.id,
                        deleted: false,
                    },
                    { $set: { deleted: true } },
                );
            }

            return new NoContentResponse({ message: "Category deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Permanently deletes a category and its associated sub-categories.
     *@param {string} userId - The ID of the user performing the operation.
     *@param {DeleteCategoryDto} deleteCategoryDto - The DTO containing the category and company IDs for deletion.
     *@returns {NoContentResponse} - A response indicating that the category has been deleted permanently.
     *@throws {HttpException} - If the deletion fails due to an exception or error.
     *@throws {InternalServerErrorException} - If an internal server error occurs.
     */
    async permDeleteCategory(companyId: string, deleteCategoryDto: DeleteCategoryDto) {
        try {
            await this.categoryModel.deleteOne({
                _id: deleteCategoryDto.id,
                companyId,
            });

            await this.subCategoryModel.deleteMany({
                companyId,
                categoryId: deleteCategoryDto.id,
            });
            return new NoContentResponse({ message: "Category deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Restores a category and its sub-categories.
     *@param userId - The ID of the user.
     *@param restoreCategoryDto - The data for restoring the category.
     *@returns A response indicating the success of the operation.
     *@throws {HttpException} If the category or sub-categories already exist.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async restoreCategory(companyId: string, restoreCategoryDto: RestoreCategoryDto) {
        try {
            const result = await this.categoryModel.updateOne(
                { _id: restoreCategoryDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            } else {
                //fetching sub-category and restoring them
                await this.subCategoryModel.updateMany(
                    {
                        companyId,
                        categoryId: restoreCategoryDto.id,
                        deleted: true,
                    },
                    { $set: { deleted: false } },
                );
            }
            return new OkResponse({ message: "Category restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCategory(userId: string, updateCategoryDto: UpdateCategoryDto) {
        try {
            const result = await this.categoryModel.updateOne(
                { _id: updateCategoryDto.categoryId, deleted: false },
                {
                    $set: { ...updateCategoryDto },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Category updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retriees a list of categories with pagination support.
     *@param userId - The ID of the user.
     *@param companyId - The ID of the company.
     *@param deleted - A flag indicating whether to include deleted categories.
     *@param paginationRequestDto - The pagination settings.
     *@returns A response containing the categories and total data count for pagination.
     *@throws {HttpException} If an error occurs while retrieving the categories.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async getCategory(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const category = await this.categoryModel.find({ companyId, deleted }).skip(offset).limit(limit);
            return new OkResponse({ category });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Retrieves a category by its ID.
     * @param userId - The user ID.
     * @param companyId - The company ID.
     * @param categoryId - The category ID.
     * @param deleted - Indicates if the category is deleted.
     * @returns A Promise that resolves to an OkResponse containing the retrieved category.
     * @throws InternalServerErrorException if an error occurs during the retrieval process.
     */
    async getCategoryById(userId: string, companyId: string, categoryId: string, deleted: boolean) {
        try {
            const category = await this.categoryModel.findOne({
                _id: categoryId,
                companyId,
                deleted,
            });
            return new OkResponse({ category });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Add default category with the specified parameters.
     * @param companyId - The ID of the company.
     * @param createdBy - The ID of the user who created the category.
     * @param categoryIdUUID - The UUID of the category ID.
     * @returns A promise that resolves to a CreatedResponse if the default category is created successfully.
     * @throws If an error occurs during the operation, an appropriate exception is thrown.
     */
    async addDefaultCategory(companyId: string, createdBy: string, categoryIdUUID) {
        try {
            const defaultValues = defaultCategory(categoryIdUUID);
            const data = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));
            await this.categoryModel.insertMany(data);
            return new CreatedResponse({
                message: "Default Category created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Create a subcategory.
     * @param userId The ID of the user.
     * @param createSubCategoryDto The data for creating the subcategory.
     * @throws HttpException If the subcategory already exists.
     * @throws InternalServerErrorException If an internal server error occurs.
     * @returns A response indicating the success of the operation.
     */
    async createSubCategory(companyId: string, createSubCategoryDto: CreateSubCategoryDto) {
        try {
            const subCategory = await this.subCategoryModel
                .exists({
                    name: createSubCategoryDto.name,
                    categoryId: createSubCategoryDto.categoryId,
                    companyId,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (subCategory) throw new HttpException("subCategory already exists", HttpStatus.BAD_REQUEST);
            const createdSubCategory = new this.subCategoryModel({
                companyId,
                ...createSubCategoryDto,
            });
            await createdSubCategory.save();
            return new CreatedResponse({ message: "SubCategory created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Deletes a subcategory.
     * @param userId - The ID of the user.
     * @param deleteSubCategoryDto - The DTO containing the subcategory ID to delete.
     * @returns A NoContentResponse indicating the success of the deletion.
     * @throws HttpException if an error occurs during the deletion process.
     */
    async deleteSubCategory(userId: string, deleteSubCategoryDto: DeleteSubCategoryDto) {
        try {
            const result = await this.subCategoryModel.updateOne(
                { _id: deleteSubCategoryDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "SubCategory deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Permanently deletes a subcategory.
     * @param userId - The ID of the user.
     * @param deleteSubCategoryDto - The DTO containing the ID and company ID of the subcategory to delete.
     * @returns A NoContentResponse indicating that the subcategory has been deleted permanently.
     * @throws HttpException if an error occurs during the deletion process.
     * @throws InternalServerErrorException if there's an internal server error.
     */
    async permDeleteSubCategory(companyId: string, deleteSubCategoryDto: DeleteSubCategoryDto) {
        try {
            await this.subCategoryModel.deleteOne({
                _id: deleteSubCategoryDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "Sub Category deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Restores a deleted subcategory.
     * @param userId - The ID of the user.
     * @param restoreSubCategoryDto - The data object containing the ID of the subcategory to restore.
     * @returns A response indicating the success of the operation.
     * @throws {HttpException} If an HTTP exception occurs during the operation.
     * @throws {InternalServerErrorException} If an internal server error occurs.
     */
    async restoreSubCategory(userId: string, restoreSubCategoryDto: RestoreSubCategoryDto) {
        try {
            const result = await this.subCategoryModel.updateOne(
                { _id: restoreSubCategoryDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Sub Category restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Updates a subcategory.
     * @param {string} userId - The ID of the user.
     * @param {UpdateSubCategoryDto} updateSubCategoryDto - The DTO containing the updated subcategory data.
     * @returns {Promise<OkResponse>} A promise that resolves to an OkResponse with a success message.
     * @throws {HttpException|InternalServerErrorException} Throws an exception if an error occurs.
     */
    async updateSubCategory(userId: string, updateSubCategoryDto: UpdateSubCategoryDto) {
        try {
            const result = await this.subCategoryModel.updateOne(
                { _id: updateSubCategoryDto.subCategoryId, deleted: false },
                {
                    $set: { ...updateSubCategoryDto },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "SubCategory updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Retrieves subcategories based on the specified parameters.
     * @param userId - The user ID.
     * @param companyId - The company ID.
     * @param deleted - Boolean indicating whether the subcategories should be deleted or not.
     * @param paginationRequestDto - Pagination request data.
     * @returns A promise that resolves to an OkResponse containing the retrieved subcategories.
     * @throws If an HttpException occurs or if there is an internal server error.
     */
    async getSubCategory(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const subCategory = await this.subCategoryModel
                .find({ companyId, deleted })
                .skip(offset)
                .limit(limit);
            return new OkResponse({ subCategory });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Retrieves a subcategory by its ID.
     * @param userId - The user ID.
     * @param companyId - The company ID.
     * @param subCategoryId - The subcategory ID.
     * @param deleted - Indicates if the subcategory is deleted.
     * @returns A response containing the retrieved subcategory.
     * @throws If an HTTP exception occurs or an internal server error is encountered.
     */
    async getSubCategoryById(userId: string, companyId: string, subCategoryId: string, deleted: boolean) {
        try {
            const subCategory = await this.subCategoryModel.findOne({
                _id: subCategoryId,
                companyId,
                deleted,
            });
            return new OkResponse({ subCategory });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Retrieves subcategories by category ID.
     * @param userId The ID of the user.
     * @param companyId The ID of the company.
     * @param categoryId The ID of the category.
     * @param deleted Specifies whether to include deleted subcategories.
     * @param paginationRequestDto The pagination options for the query.
     * @returns A response object containing the retrieved subcategories.
     * @throws InternalServerErrorException if an error occurs during the process.
     */
    async getSubCategoryByCategoryId(
        userId: string,
        companyId: string,
        categoryId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const subCategory = await this.subCategoryModel
                .find({ companyId, categoryId, deleted })
                .skip(offset)
                .limit(limit);
            return new OkResponse({ subCategory });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Adds default subcategories for a given company.
     * @param companyId - The ID of the company.
     * @param createdBy - The user who created the subcategories.
     * @param subCategoryIdUUID - The UUID of the subcategory.
     * @param categoryIdUUID - The UUID of the category.
     * @returns A promise that resolves to a CreatedResponse indicating the success of the operation.
     * @throws If an error occurs during the process, it will throw an InternalServerErrorException.
     */
    async addDefaultSubCategory(companyId: string, createdBy: string, subCategoryIdUUID, categoryIdUUID) {
        try {
            const defaultValues = defaultSubCategory(subCategoryIdUUID, categoryIdUUID);
            const data = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));
            await this.subCategoryModel.insertMany(data);
            return new CreatedResponse({
                message: "Default SubCategory created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Create a new material.
     * @param {string} userId - The user ID.
     * @param {CreateMaterialDto} createMaterialDto - The data for creating a material.
     * @returns {Promise<CreatedResponse>} - A promise that resolves to a CreatedResponse object.
     * @throws {HttpException|InternalServerErrorException} - Throws an error if the material already exists or an internal server error occurs.
     */
    async createMaterial(companyId: string, createMaterialDto: CreateMaterialDto) {
        try {
            const material = await this.materialModel
                .exists({
                    name: createMaterialDto.name,
                    companyId,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (material) throw new HttpException("Material already exists", HttpStatus.BAD_REQUEST);
            const createdMaterial = new this.materialModel({
                companyId,
                ...createMaterialDto,
            });
            await createdMaterial.save();
            return new CreatedResponse({
                material: createdMaterial,
                message: "Material created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Delete a material.
     * @param userId - User ID.
     * @param deleteMaterialDto - Delete material DTO.
     * @returns A NoContentResponse indicating the success of the deletion.
     * @throws InternalServerErrorException if an internal server error occurs.
     */
    async deleteMaterial(companyId: string, deleteMaterialDto: DeleteMaterialDto) {
        try {
            const tsk = await this.taskModel.countDocuments({
                companyId,
                material: {
                    $elemMatch: {
                        mat: deleteMaterialDto.id,
                    },
                },
                deleted: false,
            });

            if (tsk > 0)
                throw new ConflictException(
                    "The material you're trying to delete is currently in use and cannot be deleted.",
                );

            const result = await this.materialModel.updateOne(
                { _id: deleteMaterialDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                throw new InternalServerErrorException({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Material deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Permanently deletes a material.
     * @param userId The ID of the user performing the operation.
     * @param deleteMaterialDto The DTO containing the material ID and company ID.
     * @returns A NoContentResponse indicating the material has been deleted permanently.
     * @throws {HttpException} If an HttpException occurs during the operation.
     * @throws {InternalServerErrorException} If an error occurs during the operation.
     */
    async permDeleteMaterial(companyId: string, deleteMaterialDto: DeleteMaterialDto) {
        try {
            await this.materialModel.deleteOne({
                _id: deleteMaterialDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "Material deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Restores a material by updating its `deleted` flag to `false`.
     * @param {string} userId - The ID of the user performing the restore operation.
     * @param {RestoreMaterialDto} restoreMaterialDto - The DTO containing the ID of the material to restore.
     * @returns {Promise<OkResponse>} A promise that resolves to an `OkResponse` with a success message.
     * @throws {InternalServerErrorException} If an internal server error occurs during the restore operation.
     */
    async restoreMaterial(userId: string, restoreMaterialDto: RestoreMaterialDto) {
        try {
            const result = await this.materialModel.updateOne(
                { _id: restoreMaterialDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Material restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Update a material.
     * @param userId The user ID.
     * @param updateMaterialDto The data for updating the material.
     * @returns An `OkResponse` object with a success message.
     * @throws If an error occurs during the update process, an `InternalServerErrorException` is thrown.
     */
    async updateMaterial(userId: string, updateMaterialDto: UpdateMaterialDto) {
        try {
            const result = await this.materialModel.updateOne(
                { _id: updateMaterialDto.materialId, deleted: false },
                {
                    $set: { ...updateMaterialDto },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Material updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateMaterialOrder(orderId: string, updateMaterialOrderDto: UpdateMaterialOrderDto) {
        try {
            const materialsToUpdate = updateMaterialOrderDto.matList;

            const bulkOps = materialsToUpdate.map((material) => ({
                updateOne: {
                    filter: {
                        _id: orderId,
                        deleted: false,
                    },
                    update: {
                        $set: {
                            "matList.$[elem].inventory": material.inventory,
                            "matList.$[elem].sequence": material.sequence,
                            "matList.$[elem].vendor": material.vendor,
                            "matList.$[elem].amountEdit": material.amountEdit,
                            "matList.$[elem].nameEdit": material.nameEdit,
                            "matList.$[elem].unitEdit": material.unitEdit,
                            "matList.$[elem].deleted": material.deleted,
                        },
                    },
                    arrayFilters: [{ "elem._id": material._id, "elem.projectId": material.projectId }],
                    upsert: false,
                },
            }));

            // Execute the bulkWrite operation
            const result = await this.orderModel.bulkWrite(bulkOps);

            if (result.modifiedCount === 0) {
                throw new NotFoundException(
                    "No materials were updated. Please check if the material IDs are valid.",
                );
            }

            return new OkResponse({ message: "Material Order updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addMaterialsToOrder(orderId: string, addMaterialToOrderDto: AddMaterialToOrderDto) {
        try {
            // Find the order by ID
            const order = await this.orderModel.findOne({ _id: orderId, deleted: false });

            if (!order) {
                throw new NotFoundException(`Order with ID ${orderId} not found`);
            }

            // Use $push to add the new materials to the matList array
            const updatedOrder = await this.orderModel.updateOne(
                { _id: orderId }, // Find the order by ID
                {
                    $push: {
                        matList: addMaterialToOrderDto.matList, // Append the new object to the existing array
                    },
                },
            );

            return new OkResponse({ message: "Materials added successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Get project tasks for a material.
     * @param companyId The company ID.
     * @param materialId The material ID.
     * @returns An `OkResponse` object with the list of project tasks.
     * @throws If an error occurs during the retrieval process, an `InternalServerErrorException` is thrown.
     */
    async getMaterialProjectTask(companyId: string, materialId: string) {
        try {
            const projectTasks = await this.taskModel.find(
                {
                    companyId,
                    "material.mat": materialId,
                    // deleted: false,
                },
                { name: 1, active: 1, deleted: 1 },
            );

            return new OkResponse({ projectTasks });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Fetch list of tasks where this Input is used
     * @param userId The user ID.
     * @param companyId The company ID.
     * @param inputId The input ID.
     * @returns A `Promise` that resolves to an `HttpResponse` object containing the project tasks.
     */
    async getTasksUsedByInput(companyId: string, inputId: string) {
        try {
            const tasks = await this.taskModel.find(
                {
                    companyId,
                    "input.input": inputId,
                    // deleted: false,
                },
                { name: 1, active: 1, deleted: 1 },
            );

            return new OkResponse({ tasks });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Fetch list of packages and options where this task is used
     * @param userId The user ID.
     * @param companyId The company ID.
     * @param taskId The task ID.
     * @returns A `Promise` that resolves to an `HttpResponse` object containing the project tasks.
     */
    async getPackagesOptionsUsedByTask(companyId: string, taskId: string) {
        try {
            const packages = await this.packageModel.find(
                {
                    companyId,
                    taskArray: taskId,
                    // deleted: false,
                },
                { name: 1, active: 1, deleted: 1 },
            );
            const options = await this.optionModel.find(
                {
                    companyId,
                    taskArray: taskId,
                    // deleted: false,
                },
                { name: 1, active: 1, deleted: 1 },
            );

            return new OkResponse({ packages, options });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Retrieves material based on the provided parameters.
     * @param userId - The ID of the user.
     * @param companyId - The ID of the company.
     * @param deleted - Indicates if the material is deleted.
     * @param paginationRequestDto - The pagination request parameters.
     * @returns A response containing the material.
     * @throws If an error occurs during the process.
     */
    async getMaterial(userId: string, companyId: string, deleted: boolean, getMatDto: GetMatDto) {
        try {
            const query = {
                companyId,
                deleted,
                ...(getMatDto.categoryId && { categoryId: getMatDto.categoryId }),
                ...(getMatDto.subCategoryId && { subCategoryId: getMatDto.subCategoryId }),
            };

            // const limit = getMatDto.limit || 10;
            // const offset = limit * (getMatDto.skip || 0);

            const material = await this.materialModel.aggregate([
                {
                    $match: query,
                },
                {
                    $lookup: {
                        from: "Category",
                        localField: "categoryId",
                        foreignField: "_id",
                        as: "categoryResult",
                    },
                },
                {
                    $lookup: {
                        from: "SubCategory",
                        localField: "subCategoryId",
                        foreignField: "_id",
                        as: "subCategoryResult",
                    },
                },
                {
                    $lookup: {
                        from: "Unit",
                        localField: "unitId",
                        foreignField: "_id",
                        as: "unitResult",
                    },
                },
                {
                    $lookup: {
                        from: "Task",
                        let: { matId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    companyId,
                                    // deleted: false,
                                    $expr: {
                                        $in: ["$$matId", "$material.mat"],
                                    },
                                },
                            },
                            { $project: { _id: 1, name: 1, active: 1, deleted: 1 } },
                        ],
                        as: "projectTasks",
                    },
                },
                // { $skip: offset },
                // { $limit: limit },
                {
                    $project: {
                        _id: 1,
                        // companyId: 1,
                        categoryId: 1,
                        subCategoryId: 1,
                        unitId: 1,
                        name: 1,
                        cost: 1,
                        vendor: 1,
                        inv: 1,
                        description: 1,
                        categoryName: { $arrayElemAt: ["$categoryResult.name", 0] },
                        subCategoryName: { $arrayElemAt: ["$subCategoryResult.name", 0] },
                        unitName: { $arrayElemAt: ["$unitResult.name", 0] },
                        projectTasks: 1,
                    },
                },
            ]);
            // const material = await this.materialModel.find({ companyId, deleted }).skip(offset).limit(limit);
            return new OkResponse({ material });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Get material by ID.
     * @param userId - User ID.
     * @param companyId - Company ID.
     * @param materialId - Material ID.
     * @param deleted - Flag indicating if the material is deleted.
     * @returns Promise that resolves to the material.
     * @throws HttpException if an error occurs.
     * @throws InternalServerErrorException if an internal server error occurs.
     */
    async getMaterialById(userId: string, companyId: string, materialId: string, deleted: boolean) {
        try {
            const material = await this.materialModel
                .findOne({
                    _id: materialId,
                    companyId,
                    deleted,
                })
                .populate("unitId", "name", "Unit");
            return new OkResponse({ material });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Adds default material with the specified parameters.
     * @param companyId The ID of the company.
     * @param createdBy The ID of the user who created the default material.
     * @param materialUUID The UUID of the material.
     * @param categoryIdUUID The UUID of the category.
     * @param subCategoryIdUUID The UUID of the subcategory.
     * @param unitUUID The UUID of the unit.
     * @returns A promise that resolves to a CreatedResponse object.
     * @throws {HttpException} If an HTTP exception occurs.
     * @throws {InternalServerErrorException} If an internal server error occurs.
     */
    async addDefaultMaterial(
        companyId: string,
        createdBy: string,
        materialUUID,
        categoryIdUUID,
        subCategoryIdUUID,
        unitUUID,
    ) {
        try {
            const defaultValues = defaultMaterial(materialUUID, categoryIdUUID, subCategoryIdUUID, unitUUID);
            const data = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));
            await this.materialModel.insertMany(data);
            return new CreatedResponse({
                message: "Default Material created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // /**
    //  * Adds default crew positions to the database.
    //  * @param companyId The ID of the company.
    //  * @param createdBy The ID of the user who created the crew positions.
    //  * @param crewPositionUUID The UUID of the crew position.
    //  * @returns A promise that resolves to a CreatedResponse or throws an error.
    //  */
    // async addDefaultCrewPosition(companyId: string, createdBy: string, crewPositionUUID) {
    //     try {
    //         const defaultValues = defaultCrewPosition(crewPositionUUID);
    //         const data = defaultValues.map((value) => ({
    //             ...value,
    //             companyId,
    //             createdBy,
    //             deleted: false,
    //         }));
    //         await this.crewPositionModel.insertMany(data);
    //         return new CreatedResponse({
    //             message: "Default CrewPosition created successfully!",
    //         });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // /**
    //  * Create a crew position.
    //  * @param userId The ID of the user.
    //  * @param createCrewPositionDto The DTO containing the data for creating the crew position.
    //  * @returns A promise that resolves to a CreatedResponse indicating the success of the operation.
    //  * @throws HttpException if the crew position already exists or an internal server error occurs.
    //  */
    // async createCrewPosition(userId: string, createCrewPositionDto: CreateCrewPositionDto) {
    //     try {
    //         const crewPosition = await this.crewPositionModel
    //             .findOne({
    //                 title: createCrewPositionDto.title,
    //                 companyId: createCrewPositionDto.companyId,
    //                 deleted: false,
    //             })
    //             .exec();
    //         if (crewPosition) throw new HttpException("Crew Position already exists", HttpStatus.BAD_REQUEST);
    //         const createdCrewPosition = new this.crewPositionModel({
    //             ...createCrewPositionDto,
    //         });
    //         await createdCrewPosition.save();
    //         return new CreatedResponse({ message: "Crew Position created successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // /**
    //  * Deletes a crew position.
    //  * @param userId The user ID.
    //  * @param deleteCrewPositionDto The DTO containing the crew position ID to delete.
    //  * @throws {HttpException} If an error occurs during the operation.
    //  * @throws {InternalServerErrorException} If an internal server error occurs.
    //  * @returns A response indicating the success of the deletion.
    //  */
    // async deleteCrewPosition(userId: string, deleteCrewPositionDto: DeleteCrewPositionDto) {
    //     try {
    //         const task = await this.taskModel.find({
    //             "labor.worker": deleteCrewPositionDto.id,
    //             deleted: false,
    //         });

    //         if (task.length)
    //             throw new ConflictException(
    //                 "The material you're trying to delete is currently in use and cannot be deleted.",
    //             );

    //         const result = await this.crewPositionModel.updateOne(
    //             { _id: deleteCrewPositionDto.id, deleted: false },
    //             {
    //                 $set: { deleted: true },
    //             },
    //         );

    //         if (result.modifiedCount === 0) {
    //             return new OkResponse({ message: "Failed to update changes!" });
    //         }

    //         return new NoContentResponse({ message: "Crew Position deleted successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // /**
    //  * Deletes a crew position permanently.
    //  * @param userId The ID of the user.
    //  * @param deleteCrewPositionDto The DTO containing the crew position ID and company ID.
    //  * @returns A NoContentResponse with a success message if the crew position is deleted permanently.
    //  * @throws HttpException if an error occurs or the crew position is not found.
    //  * @throws InternalServerErrorException if an internal server error occurs.
    //  */
    // async permDeleteCrewPosition(userId: string, deleteCrewPositionDto: DeleteCrewPositionDto) {
    //     try {
    //         await this.crewPositionModel.deleteOne({
    //             _id: deleteCrewPositionDto.id,
    //             companyId: deleteCrewPositionDto.companyId,
    //         });
    //         return new NoContentResponse({ message: "Crew Position deleted permanently!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // /**
    //  * Restores a crew position.
    //  * @param userId The ID of the user.
    //  * @param restoreCrewPositionDto The DTO containing the crew position ID to restore.
    //  * @returns A response indicating the success of the operation.
    //  * @throws InternalServerErrorException if an internal server error occurs.
    //  */
    // async restoreCrewPosition(userId: string, restoreCrewPositionDto: RestoreCrewPositionDto) {
    //     try {
    //         const result = await this.crewPositionModel.updateOne(
    //             { _id: restoreCrewPositionDto.id, deleted: true },
    //             {
    //                 $set: { deleted: false },
    //             },
    //         );

    //         if (result.modifiedCount === 0) {
    //             return new OkResponse({ message: "Not restored!" });
    //         }

    //         return new OkResponse({ message: "Crew Position restored successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // /**
    //  * Update a crew position.
    //  * @param userId The ID of the user.
    //  * @param updateCrewPositionDto The DTO containing the crew position data to be updated.
    //  * @returns A response indicating the success of the operation.
    //  * @throws InternalServerErrorException if an internal server error occurs.
    //  */
    // async updateCrewPosition(userId: string, updateCrewPositionDto: UpdateCrewPositionDto) {
    //     try {
    //         const result = await this.crewPositionModel.updateOne(
    //             { _id: updateCrewPositionDto.crewPositionId, deleted: false },
    //             {
    //                 $set: { ...updateCrewPositionDto },
    //             },
    //         );

    //         if (result.modifiedCount === 0) {
    //             return new OkResponse({ message: "Failed to update changes!" });
    //         }

    //         return new OkResponse({ message: "Crew Position updated successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // /**
    //  * Retrieves crew positions based on the provided parameters.
    //  * @param userId - The ID of the user.
    //  * @param companyId - The ID of the company.
    //  * @param deleted - Indicates whether deleted crew positions should be included.
    //  * @param paginationRequestDto - The pagination parameters for the query.
    //  * @returns A response containing the retrieved crew positions.
    //  * @throws {InternalServerErrorException} If an internal server error occurs.
    //  */
    // async getCrewPosition(
    //     userId: string,
    //     companyId: string,
    //     deleted: boolean,
    //     paginationRequestDto: PaginationRequestDto,
    // ) {
    //     try {
    //         const limit = paginationRequestDto.limit || 10;
    //         const offset = limit * (paginationRequestDto.skip || 0);
    //         const crewPosition = await this.crewPositionModel
    //             .find({ companyId, deleted })
    //             .skip(offset)
    //             .limit(limit);
    //         return new OkResponse({ crewPosition });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // /**
    //  * Retrieves a crew position by its ID.
    //  * @param userId - The ID of the user.
    //  * @param companyId - The ID of the company.
    //  * @param crewPositionId - The ID of the crew position.
    //  * @param deleted - Boolean indicating whether the crew position is deleted.
    //  * @returns A Promise that resolves to an OkResponse containing the crew position.
    //  * @throws InternalServerErrorException if an unexpected error occurs.
    //  * @throws HttpException if an HTTP-related error occurs.
    //  */
    // async getCrewPositionById(userId: string, companyId: string, crewPositionId: string, deleted: boolean) {
    //     try {
    //         const crewPosition = await this.crewPositionModel.findOne({
    //             _id: crewPositionId,
    //             companyId,
    //             deleted,
    //         });
    //         return new OkResponse({ crewPosition });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    async createProjectType(companyId: string, createProjectTypeDto: CreateProjectTypeDto) {
        try {
            const projectType = await this.projectTypeModel
                .exists({
                    name: createProjectTypeDto.name,
                    companyId,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (projectType) throw new HttpException("Project Type already exists", HttpStatus.BAD_REQUEST);
            const {
                salesCommision: { jobCompletion, jobSale, jobStart },
            } = createProjectTypeDto;

            if (jobCompletion + jobSale + jobStart > 1)
                throw new HttpException(
                    "Commission setting sum can't be more then 100%",
                    HttpStatus.BAD_REQUEST,
                );

            const createdProjectType = new this.projectTypeModel({
                companyId,
                ...createProjectTypeDto,
            });
            await createdProjectType.save();
            return new CreatedResponse({ message: "ProjectType created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteProjectType(userId: string, { id }: DeleteProjectTypeDto) {
        try {
            // Check if project type is in use
            const [oppCount, projectCount, inputCount, packageCount, optionCount, taskCount] =
                await Promise.all([
                    this.opportunityModel.countDocuments({ oppType: id }),
                    this.projectModel.countDocuments({ projectType: id }),
                    this.inputModel.countDocuments({ projectType: id }),
                    this.packageModel.countDocuments({ type: id }),
                    this.optionModel.countDocuments({ type: id }),
                    this.taskModel.countDocuments({ type: id }),
                ]);

            if (
                oppCount > 0 ||
                projectCount > 0 ||
                inputCount > 0 ||
                packageCount > 0 ||
                optionCount > 0 ||
                taskCount > 0
            ) {
                const errorMessage = `Cannot delete project type. It is in use: Opportunities: ${oppCount}, Projects: ${projectCount}, Inputs: ${inputCount}, Packages: ${packageCount}, Options: ${optionCount}, Tasks: ${taskCount}`;
                throw new ConflictException(errorMessage);
            }

            const result = await this.projectTypeModel.updateOne(
                { _id: id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "ProjectType deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteProjectType(companyId: string, { id }: DeleteProjectTypeDto) {
        try {
            // Check if project type is in use
            const [oppCount, projectCount, inputCount, packageCount, optionCount, taskCount] =
                await Promise.all([
                    this.opportunityModel.countDocuments({ oppType: id }),
                    this.projectModel.countDocuments({ projectType: id }),
                    this.inputModel.countDocuments({ projectType: id }),
                    this.packageModel.countDocuments({ type: id }),
                    this.optionModel.countDocuments({ type: id }),
                    this.taskModel.countDocuments({ type: id }),
                ]);

            if (
                oppCount > 0 ||
                projectCount > 0 ||
                inputCount > 0 ||
                packageCount > 0 ||
                optionCount > 0 ||
                taskCount > 0
            ) {
                const errorMessage = `Cannot delete project type. It is in use: Opportunities: ${oppCount}, Projects: ${projectCount}, Inputs: ${inputCount}, Packages: ${packageCount}, Options: ${optionCount}, Tasks: ${taskCount}`;
                throw new ConflictException(errorMessage);
            }

            await this.projectTypeModel.deleteOne({
                _id: id,
                companyId,
            });
            return new NoContentResponse({ message: "ProjectType deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreProjectType(userId: string, restoreProjectTypeDto: RestoreProjectTypeDto) {
        try {
            const result = await this.projectTypeModel.updateOne(
                { _id: restoreProjectTypeDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Project Type restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateProjectType(companyId: string, updateProjectTypeDto: UpdateProjectTypeDto) {
        try {
            // check for duplicate name
            const projectType = await this.projectTypeModel
                .findOne({
                    _id: { $ne: updateProjectTypeDto.projectTypeId },
                    name: updateProjectTypeDto.name,
                    companyId,
                    deleted: false,
                })
                .exec();

            if (projectType) throw new HttpException("Project Type already exists", HttpStatus.BAD_REQUEST);

            const result = await this.projectTypeModel.updateOne(
                { _id: updateProjectTypeDto.projectTypeId, deleted: false },
                {
                    $set: { ...updateProjectTypeDto },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "ProjectType updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Function to get group name that are in use by tasks
     * @param companyId
     * @param projectTypeId
     */
    async projectTypeGroupCheck(companyId: string) {
        try {
            const uniqueGroups = await this.taskModel.aggregate([
                { $match: { companyId, deleted: false } },
                { $group: { _id: { type: "$type" }, groups: { $addToSet: "$group" } } },
                { $addFields: { _id: "$_id.type" } },
                { $project: { _id: 1, groups: 1 } },
            ]);

            return new OkResponse({ uniqueGroups });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getProjectType(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const projectType = await this.projectTypeModel
                .find({ companyId, deleted })
                .skip(offset)
                .limit(limit);
            return new OkResponse({ projectType });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getProjectTypeById(userId: string, companyId: string, projectTypeId: string, deleted: boolean) {
        try {
            const projectType = await this.projectTypeModel.findOne({
                _id: projectTypeId,
                companyId,
                deleted,
            });
            return new OkResponse({ projectType });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultProjectType(companyId: string, memberId: string, projectTypeUUID, taskGroup) {
        try {
            const defaultValues = addDefaultProjectType(projectTypeUUID, taskGroup);
            const inputData = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy: memberId,
                deleted: false,
            }));
            await this.projectTypeModel.insertMany(inputData);
            return new CreatedResponse({
                message: "Default Project created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createTask(companyId: string, createTaskDto: CreateTaskDto) {
        try {
            const task = await this.taskModel
                .exists({
                    name: createTaskDto.name,
                    companyId,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (task) throw new HttpException("Task already exists", HttpStatus.BAD_REQUEST);
            createTaskDto.input = createTaskDto.input;
            createTaskDto.material = createTaskDto.material;
            createTaskDto.labor = createTaskDto.labor;
            createTaskDto.waste = createTaskDto.waste / 100 + 1;
            const createdTask = new this.taskModel({
                companyId,
                ...createTaskDto,
            });
            await createdTask.save();
            return new CreatedResponse({ message: "Task created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteTask(userId: string, deleteTaskDto: DeleteTaskDto) {
        try {
            const packages = await this.packageModel
                .find({
                    $or: [{ taskArray: deleteTaskDto.id }],
                })
                .select("name");

            const options = await this.optionModel
                .find({
                    $or: [{ taskArray: deleteTaskDto.id }],
                })
                .select("name");

            const packageNames = packages.map((pkg) => pkg.name);
            const optionNames = options.map((option) => option.name);

            if (packageNames.length > 0 || optionNames.length > 0) {
                // Build the error message with package and option names
                let errorMessage = "Can't delete Task! It's being used by";
                if (packageNames.length > 0) {
                    errorMessage += ` Package(s): ${packageNames.join(", ")}`;
                }
                if (optionNames.length > 0) {
                    errorMessage += ` and Option(s): ${optionNames.join(", ")}`;
                }
                throw new ConflictException(errorMessage);
            }

            const result = await this.taskModel.updateOne(
                { _id: deleteTaskDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                throw new InternalServerErrorException({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Task deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteTask(companyId: string, deleteTaskDto: DeleteTaskDto) {
        try {
            await this.taskModel.deleteOne({
                _id: deleteTaskDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "Task deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreTask(userId: string, restoreTaskDto: RestoreTaskDto) {
        try {
            const result = await this.taskModel.updateOne(
                { _id: restoreTaskDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Task restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateTask(companyId: string, updateTaskDto: UpdateTaskDto) {
        try {
            const task: any = await this.taskModel
                .exists({
                    _id: updateTaskDto.taskId,
                    companyId,
                })
                .exec();
            if (!task) throw new HttpException("Task does not exist", HttpStatus.BAD_REQUEST);
            let validate = true;
            updateTaskDto.input.map((input, idx) => {
                if (!input.input) {
                    validate = false;
                }
                if (!input.oper || idx === 0) input.oper = "+";
            });
            // If all mats removed, set to none
            if (!updateTaskDto.material.length) {
                updateTaskDto.material.push({ tMat: "MatId1", mat: "none", cov: 0 });
            }
            // If mat set to none - change cov to 0 and remove other elements
            if (updateTaskDto.material[0].mat === "none") {
                updateTaskDto.material[0].cov = 0;
                updateTaskDto.material.splice(1);
            }
            updateTaskDto.material.map((mat, idx) => {
                if (mat.mat === "none") {
                } else if (!mat.mat || !mat.cov) {
                    validate = false;
                }
            });
            updateTaskDto.labor.map((labor, idx) => {
                if (!labor.worker || !labor.time || !labor.mod) {
                    validate = false;
                }
            });
            const waste = updateTaskDto.material[0].mat === "none" ? 0 : updateTaskDto.waste;
            updateTaskDto.waste = waste / 100 + 1;

            // Access all the options which contain this task
            const options = await this.optionModel.find({
                companyId,
                taskArray: updateTaskDto.taskId,
            });

            if (!updateTaskDto.active) {
                const packages = await this.packageModel
                    .find({
                        companyId,
                        taskArray: { $in: [updateTaskDto.taskId] },
                    })
                    .select("name");

                const packageNames = packages.map((pkg) => pkg.name);
                const optionNames = options.map((option) => option.name);

                if (packageNames.length > 0 || optionNames.length > 0) {
                    // Build the error message with package and option names
                    let errorMessage = "Can't inactivate Task! It's being used by";
                    if (packageNames.length > 0) {
                        errorMessage += ` Package(s): ${packageNames.join(", ")};`;
                    }
                    if (optionNames.length > 0) {
                        errorMessage += ` Option(s): ${optionNames.join(", ")};`;
                    }
                    throw new ConflictException(errorMessage);
                }
            }

            if (updateTaskDto.group) {
                const oldGroup = task.group;
                const newGroup = updateTaskDto.group;

                const allTasks = await this.taskModel.find({ companyId });
                for (const option of options) {
                    const { selectedGroups, taskArray } = option;

                    const tasksData = allTasks.filter(
                        (task) =>
                            task._id.toString() !== updateTaskDto.taskId.toString() &&
                            task.group === oldGroup &&
                            taskArray.includes(task._id.toString()),
                    );

                    if (!tasksData.length) {
                        // Remove the oldGroup from selectedGroups of the current option
                        const groupIndex = selectedGroups.indexOf(oldGroup);
                        if (groupIndex > -1) {
                            selectedGroups.splice(groupIndex, 1);
                        }
                    }

                    // Check if the newGroup exists in selectedGroups, if not, add it
                    if (!selectedGroups.includes(newGroup)) {
                        selectedGroups.push(newGroup);
                    }

                    // Save the updated option document
                    option.selectedGroups = selectedGroups;
                    //TODO: optimise it later
                    await this.optionModel.updateOne({ _id: option._id }, { $set: { selectedGroups } });
                    // await option.save();
                }
            }

            if (validate) {
                const result = await this.taskModel.updateOne(
                    {
                        _id: updateTaskDto.taskId,
                        companyId,
                    },
                    {
                        ...updateTaskDto,
                    },
                );

                if (result.modifiedCount === 0) {
                    throw new BadRequestException({ message: "Failed to update changes!" });
                }
                return new OkResponse({ message: "Task updated successfully!" });
            }

            throw new BadRequestException({ message: "Failed to update changes! Data validation Failed" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateTaskSequence(companyId: string, updateTaskSequenceDto: UpdateSequenceDto) {
        const { data } = updateTaskSequenceDto;

        if (!data?.length) {
            return new OkResponse({ message: "Nothing to updates!" });
        }

        try {
            const bulkOperations = data.map(({ _id, sequence }) => ({
                updateOne: {
                    filter: { _id, companyId },
                    update: { $set: { order: sequence } },
                },
            }));

            const result = await this.taskModel.bulkWrite(bulkOperations, { ordered: false });

            if (result?.modifiedCount > 0) {
                return new OkResponse({ message: "Task sequences updated successfully" });
            }

            return new OkResponse({ message: "No sequences were updated!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getTask(userId: string, companyId: string, deleted: boolean, getTaskDto: GetTaskDto) {
        try {
            const limit = getTaskDto.limit || 10;
            const offset = limit * (getTaskDto.skip || 0);
            const query = {
                companyId,
                deleted,
                ...(getTaskDto.type && { type: getTaskDto.type }),
                ...(getTaskDto.group && { group: getTaskDto.group }),
                ...(getTaskDto.search && { name: { $regex: getTaskDto.search, $options: "i" } }),
            };

            // const task = await this.taskModel.find(query).skip(offset).limit(limit);
            const task = await this.taskModel.aggregate([
                { $match: query },
                { $skip: offset },
                { $limit: limit },
                {
                    $lookup: {
                        from: "Package",
                        let: { taskId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    companyId,
                                    $expr: {
                                        $in: ["$$taskId", "$taskArray"],
                                    },
                                },
                            },
                            { $project: { _id: 1, name: 1, active: 1, deleted: 1 } },
                        ],
                        as: "packages",
                    },
                },
                {
                    $lookup: {
                        from: "Options",
                        let: { taskId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    companyId,
                                    $expr: {
                                        $in: ["$$taskId", "$taskArray"],
                                    },
                                },
                            },
                            { $project: { _id: 1, name: 1, active: 1, deleted: 1 } },
                        ],
                        as: "options",
                    },
                },
            ]);

            return new OkResponse({ task });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getTaskById(userId: string, companyId: string, taskId: string, deleted: boolean) {
        try {
            const task = await this.taskModel.findOne({
                _id: taskId,
                companyId,
                deleted,
            });
            return new OkResponse({ task });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultTasks(
        companyId: string,
        createdBy: string,
        inputUUID,
        materialUUID,
        crewPositionUUID,
        tasksUUID,
        projectTypeUUID,
        unitUUID,
        taskGroup,
    ) {
        try {
            const defaultValues = defaultTasks(
                inputUUID,
                materialUUID,
                crewPositionUUID,
                tasksUUID,
                projectTypeUUID,
                unitUUID,
                taskGroup,
            );
            const data = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));
            await this.taskModel.insertMany(data);
            return new CreatedResponse({
                message: "Default Tasks created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Creates a new tax jurisdiction for a given user.
     *@param {string} userId - The ID of the user creating the tax jurisdiction.
     *@param {CreateTaxDto} createTaxDto - The data to create the tax jurisdiction with.
     *@returns {Promise<CreatedResponse>} - A Promise resolving to a CreatedResponse object.
     *@throws {HttpException} If the tax jurisdiction already exists.
     *@throws {InternalServerErrorException} If an error occurs while creating the tax jurisdiction.
     */
    async createTaxJurisdictions(companyId: string, createTaxDto: CreateTaxJurisdictionDto) {
        try {
            const tax = await this.taxModel
                .exists({
                    name: createTaxDto.name,
                    city: createTaxDto.city,
                    state: createTaxDto.state,
                    companyId,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (tax) throw new HttpException("Tax already exists", HttpStatus.BAD_REQUEST);
            const createdTax = new this.taxModel({
                companyId,
                ...createTaxDto,
            });
            await createdTax.save();
            return new CreatedResponse({ tax: createdTax, message: "Tax created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Deletes a tax jurisdiction from the database.
     *@param userId - The ID of the user making the request.
     *@param deleteTaxDto - The data transfer object containing the ID of the tax jurisdiction to be deleted.
     *@throws {HttpException} - Throws an HTTP exception if the tax jurisdiction does not exist or if there is a server error.
     *@throws {InternalServerErrorException} - Throws an internal server error exception if there is an unexpected error.
     *@returns {NoContentResponse} - Returns a response indicating that the tax jurisdiction was deleted successfully.
     */
    async deleteTaxJurisdictions(userId: string, deleteTaxDto: DeleteTaxJurisdictionDto) {
        try {
            const result = await this.taxModel.updateOne(
                { _id: deleteTaxDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Tax deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Delete a tax jurisdiction permanently.
     *@param userId The ID of the user making the request.
     *@param deleteTaxDto The DTO containing the ID of the tax jurisdiction to be deleted and the ID of the company it belongs to.
     *@returns A NoContentResponse with a success message if the tax jurisdiction was deleted successfully.
     *@throws HttpException with a message "Tax not found" if the tax jurisdiction with the given ID and company ID was not found.
     *@throws InternalServerErrorException with the error message if any other error occurs during the deletion process.
     */
    async permDeleteTaxJurisdictions(companyId: string, deleteTaxDto: DeleteTaxJurisdictionDto) {
        try {
            await this.taxModel.deleteOne({
                _id: deleteTaxDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "Tax deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Update a tax jurisdiction with the given data
     *@param userId - The ID of the user making the request
     *@param updateTaxDto - The data to update the tax jurisdiction with
     *@returns A success response with a message indicating the tax jurisdiction was updated successfully
     *@throws HttpException if the tax jurisdiction does not exist or if there is an internal server error
     */
    async updateTaxJurisdictions(userId: string, updateTaxDto: UpdateTaxJurisdictionDto) {
        try {
            const result = await this.taxModel.updateOne(
                { _id: updateTaxDto.taxId, deleted: false },
                {
                    $set: { ...updateTaxDto },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Tax updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves tax jurisdictions for a given company ID, including deleted records if requested.
     *@param userId - The ID of the user making the request.
     *@param companyId - The ID of the company for which to retrieve tax jurisdictions.
     *@param deleted - Whether to include deleted tax jurisdictions in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of tax jurisdictions.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    async getTaxJurisdictions(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const tax = await this.taxModel
                .find({ companyId, deleted })
                .sort({ name: 1 })
                .skip(offset)
                .limit(limit);
            return new OkResponse({ tax });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves the tax jurisdictions by its ID.
     *@param {string} userId - The ID of the user.
     *@param {string} companyId - The ID of the company.
     *@param {string} taxId - The ID of the tax jurisdiction.
     *@param {boolean} deleted - A flag indicating whether the tax jurisdiction is deleted or not.
     *@returns {Promise<OkResponse>} An object containing the retrieved tax jurisdiction.
     *@throws {HttpException} If an HTTP exception occurs.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async getTaxJurisdictionsById(userId: string, companyId: string, taxId: string, deleted: boolean) {
        try {
            const tax = await this.taxModel.findOne({
                _id: taxId,
                companyId,
                deleted,
            });
            return new OkResponse({ tax });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Restores a deleted tax jurisdiction.
     *@param userId The ID of the user performing the operation.
     *@param restoreTaxDto The DTO containing the ID of the tax to be restored.
     *@returns An OkResponse with a success message.
     *@throws An HttpException if the tax with the specified ID is not found or if an internal server error occurs.
     */
    async restoreTax(userId: string, restoreTaxDto: RestoreTaxJurisdictionDto) {
        try {
            const result = await this.taxModel.updateOne(
                { _id: restoreTaxDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Tax restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultTaxJurisdictions(companyId: string, createdBy: string) {
        try {
            const unitData = defaultTaxJurisdictions.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));
            await this.taxModel.insertMany(unitData);
            return new CreatedResponse({
                message: "Tax Jurisdictions Setting created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addProject(createProjectDto: any, companyId: string, typeName: string, projectName: string) {
        try {
            let createdProject;
            const currDate = createProjectDto?.currDate;
            //deleting newProject key only required to check if new project or not
            delete createProjectDto?.currDate;
            delete createProjectDto.newProject;

            if (createProjectDto?.projectId) {
                createdProject = await this.projectModel.findOneAndUpdate(
                    {
                        _id: createProjectDto.projectId,
                    },
                    {
                        ...createProjectDto,
                    },
                    { new: true, upsert: true },
                );

                // creating opp activity
                await this.opportunityService.updateOpportunityActivity(companyId, {
                    body: `updated ${projectName} Project for ${typeName}`,
                    currDate,
                    id: createProjectDto.oppId,
                    memberId: createProjectDto.createdBy,
                });
            } else {
                createdProject = await this.projectModel.create({
                    companyId,
                    ...createProjectDto,
                });

                // creating opp activity
                await this.opportunityService.updateOpportunityActivity(companyId, {
                    body: `created New ${projectName} Project for ${typeName}`,
                    currDate,
                    id: createProjectDto.oppId,
                    memberId: createProjectDto.createdBy,
                });
            }
            return createdProject._id;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    getProjectTaskPrices(tasks, materials, labor, variables, units) {
        const materialLookup = {};
        const laborLookup = {};
        const unitLookup = {};

        units.forEach((unit) => {
            unitLookup[unit._id] = unit.symbol;
        });

        materials.forEach((material) => {
            materialLookup[material._id] = {
                categoryId: material.categoryId,
                subCategoryId: material.subCategoryId,
                name: material.name,
                description: material.description,
                unitId: material.unitId,
                cost: material.cost,
                vendor: material.vendor,
                inv: material.inv,
            };
        });

        labor.forEach((position) => {
            laborLookup[position._id] = {
                title: position.name,
                rate: position.rate,
            };
        });

        tasks.forEach((task) => {
            task.tUnit = unitLookup[task.tUnit];
            task.tMat = task.tMat.map((tMat) => {
                const { mat, cov } = tMat;
                const newTMat: any = {};
                const material = materialLookup[mat];
                if (material) {
                    newTMat.mat = mat;
                    // newTMat.matCat = material.categoryId;
                    // newTMat.matSubcat = material.subCategoryId;
                    newTMat.matName = material.name;
                    newTMat.matDesc = material.description;
                    newTMat.matUnit = unitLookup[material.unitId];
                    newTMat.matCost = roundTo2(task.tValue / cov) * material.cost;
                    newTMat.matVendor = material.vendor;
                    newTMat.matInv = material.inv;
                    newTMat.matAmount = roundTo2(task.tValue / cov);
                } else {
                    newTMat.matAmount = 0;
                    newTMat.matCost = 0;
                }
                return newTMat;
            });

            task.tLabor = task.tLabor.map((tLabor) => {
                const { worker, time } = tLabor;
                const newLabor: any = {};
                const position = laborLookup[worker];
                if (position) {
                    const laborWaste = variables.laborWaste / 60 / 8; // 60 min & 8 hrs a day
                    const rawMinutes = roundTo2(task.tRawVal * time);
                    const modMinutes = roundTo2(rawMinutes * task.tMod);
                    newLabor.worker = worker;
                    newLabor.rawMinutes = rawMinutes;
                    newLabor.modMinutes = modMinutes;
                    newLabor.waste = roundTo2(modMinutes * laborWaste);
                    newLabor.ttlMinutes = roundTo2(modMinutes + newLabor.waste);
                    newLabor.ttlHours = roundTo2(newLabor.ttlMinutes / 60);
                    newLabor.cost = roundTo2(newLabor.ttlHours * position.rate);
                    newLabor.posTitle = position.title;
                    newLabor.posRate = position.rate;
                }
                return newLabor;
            });

            task.matCost = task.tMat.reduce((sum, tMat) => sum + tMat.matCost, 0);
            task.labCost = task.tLabor.reduce((sum, tLabor) => sum + tLabor.cost, 0);
        });

        return tasks;
    }

    async getRoofSizeDetail(reroofAreas, pitchSetting, typeReplacement: boolean) {
        try {
            const pitchMods = {};
            for (const key in pitchSetting) {
                if (Object.hasOwnProperty.call(pitchSetting, key)) {
                    const modifiedKey = key.split("/")[0];
                    pitchMods[modifiedKey] = pitchSetting[key];
                }
            }

            // no need to calculate rest of calculation & retuning pitch mod for non roof replacement types project
            if (!typeReplacement) {
                return {
                    pitchMod: parseInt(reroofAreas) >= 20 ? 1 + pitchMods["20"] : 1 + pitchMods[reroofAreas],
                };
            }

            let rmvFlat = 0,
                rmvLow = 0,
                rmvSteep = 0,
                rmvFlatAdd = 0,
                rmvLowAdd = 0,
                rmvSteepAdd = 0,
                instFlat = 0,
                instLow = 0,
                instSteep = 0,
                instPly = 0,
                rmvPly = 0,
                noAccess = 0,
                twoStory = 0,
                ventArea = 0,
                instMod = 0,
                rmvMod = 0,
                rmvAddMod = 0,
                plyMod = 0;

            for (const reroofArea of reroofAreas) {
                // group SQ into flat, low, and steep
                if (reroofArea.pitch >= 0 && reroofArea.pitch < 2) {
                    instFlat += reroofArea.install;
                } else if (reroofArea.pitch < 4) {
                    instLow += reroofArea.install;
                } else {
                    instSteep += reroofArea.install;
                }

                if (reroofArea.layers >= 1) {
                    if (reroofArea.pitch >= 0 && reroofArea.pitch < 2) {
                        rmvFlat += reroofArea.install;
                        rmvFlatAdd += (reroofArea.layers - 1) * reroofArea.install;
                    } else if (reroofArea.pitch < 4) {
                        rmvLow += reroofArea.install;
                        rmvLowAdd += (reroofArea.layers - 1) * reroofArea.install;
                    } else {
                        rmvSteep += reroofArea.install;
                        rmvSteepAdd += (reroofArea.layers - 1) * reroofArea.install;
                    }
                }

                instPly += reroofArea.instPly;
                rmvPly += reroofArea.rmvPly;
                noAccess += reroofArea.noAccess;
                twoStory += reroofArea.twoStory;
                ventArea += reroofArea.ventArea;

                // get roof area modifiers - pitchMods start at 0:0
                const pitchModKeys = Object.keys(pitchMods);

                // If reroofArea.pitch is greater than the highest pitchModKeys
                if (reroofArea.pitch > pitchModKeys.length - 1) {
                    const highPitch: number = +pitchMods[pitchModKeys[pitchModKeys.length - 1]];

                    instMod += reroofArea.install * (1 + highPitch);
                    rmvMod += reroofArea.remove * (1 + highPitch);
                    if (reroofArea.layers >= 1) {
                        rmvAddMod += (reroofArea.layers - 1) * reroofArea.install * (1 + highPitch);
                    }
                } else {
                    for (let j = 0; j < pitchModKeys.length; j++) {
                        const currentPitchMod: number = +pitchMods[pitchModKeys[j]];

                        // Example: if pitch is 6.1 or 6.9, use pitchMod of 7, or if pitch is 6 then use pitchMod of 6
                        if (reroofArea.pitch > j - 1 && reroofArea.pitch <= j) {
                            instMod += reroofArea.install * (1 + currentPitchMod);
                            rmvMod += reroofArea.remove * (1 + currentPitchMod);
                            if (reroofArea.layers >= 1) {
                                rmvAddMod +=
                                    (reroofArea.layers - 1) * reroofArea.install * (1 + currentPitchMod);
                            }
                            break;
                        }
                    }
                }

                if (reroofArea.instPly > 0) {
                    plyMod += reroofArea.instPly * (1 + pitchMods[0]);
                }
            }

            rmvMod /= rmvFlat + rmvLow + rmvSteep;
            rmvAddMod /= rmvFlatAdd + rmvLowAdd + rmvSteepAdd;
            instMod /= instFlat + instLow + instSteep;
            plyMod /= instPly;

            return {
                rmvFlat,
                rmvLow,
                rmvSteep,
                rmvFlatAdd,
                rmvLowAdd,
                rmvSteepAdd,
                instFlat,
                instLow,
                instSteep,
                instPly,
                rmvPly,
                noAccess,
                twoStory,
                ventArea,
                rmvMod,
                rmvAddMod,
                instMod,
                plyMod,
            };
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    loadProjectTasksArray(projectInputs, typeTasks, roofSizeDetail) {
        const projectTasks = [];
        const operatorLookup = {
            "+": (a, b) => a + b,
            "-": (a, b) => a - b,
            "*": (a, b) => a * b,
            "/": (a, b) => a / b,
        };

        // apply the appropriate modifier from roof areas to the labor time
        const laborModifiers = {
            rmvMod: roofSizeDetail.rmvMod,
            rmvAddMod: roofSizeDetail.rmvAddMod,
            instMod: roofSizeDetail.instMod,
            plyMod: roofSizeDetail.plyMod,
            pitchMod: roofSizeDetail.pitchMod,
        };
        for (const typeTask of typeTasks) {
            const { _id, type, name, unit, waste, input, material, labor, order, group } = typeTask;

            let tMod = 1;
            for (const lab of labor) {
                if (laborModifiers.hasOwnProperty(lab.mod)) {
                    tMod = laborModifiers[lab.mod];
                    break;
                }
            }

            // get how many of the task is needed to complete the project
            let tRawVal = 0;
            // LEVEL 2: Go through each input of THIS task and increase it by the project input
            for (const inp of input) {
                if (isNaN(inp.input)) {
                    for (const projectInput of projectInputs) {
                        if (inp.input === projectInput._id) {
                            tRawVal = operatorLookup[inp.oper](tRawVal, projectInput.value);
                            break;
                        }
                    }
                } else {
                    tRawVal = operatorLookup[inp.oper](tRawVal, inp.input);
                }
            }

            // const tRawVal = tValue;
            if (tRawVal > 0) {
                projectTasks.push({
                    _id,
                    tType: type,
                    tName: name,
                    tUnit: unit,
                    tMat: material,
                    tLabor: labor,
                    tRawVal,
                    tValue: roundTo2(tRawVal * waste),
                    tMod,
                    order,
                    group,
                });
            }
        }
        return projectTasks;
    }

    async upsertProject(companyId: string, createProjectDto: CreateProjectDto) {
        try {
            //if project is new checking if name already exist
            if (createProjectDto.newProject) {
                const project = await this.projectModel
                    .findOne({
                        companyId,
                        oppId: createProjectDto.oppId,
                        name: createProjectDto.name,
                        deleted: false,
                    })
                    .exec();
                if (project) throw new HttpException("Project already exists", HttpStatus.BAD_REQUEST);
            }

            const thisOpp: any = await this.opportunityModel
                .findOne({
                    companyId,
                    _id: createProjectDto.oppId,
                    deleted: { $ne: true },
                })
                .exec();

            const [variables, projectTypes, materials, labor, units, packages, options, typeTasks, tax] =
                await Promise.all([
                    this.companySettingModel
                        .findOne({ companyId })
                        .select(
                            "ttlBurden matMarkup modOH modP modS financeMod laborWaste travelHrlyRate manHourRate plywoodRate plywoodLaborRate",
                        )
                        .lean(),
                    this.projectTypeModel
                        .findOne({
                            _id: createProjectDto.projectType,
                            companyId,
                            deleted: { $ne: true },
                        })
                        .exec(),
                    this.materialModel
                        .find({
                            companyId,
                            deleted: { $ne: true },
                        })
                        .exec(),
                    // this.crewPositionModel
                    //     .find({
                    //         companyId,
                    //         deleted: { $ne: true },
                    //     })
                    //     .exec(),
                    this.workTaskModel.find({ companyId, delete: false }).exec(),
                    this.unitModel
                        .find({
                            companyId,
                            deleted: { $ne: true },
                        })
                        .exec(),
                    this.packageModel
                        .find({
                            type: createProjectDto.projectType,
                            companyId,
                            deleted: { $ne: true },
                            active: { $ne: false },
                        })
                        .sort({ order: 1 })
                        .select("description name order upsell taskArray")
                        .exec(),
                    this.optionModel
                        .find({
                            type: createProjectDto.projectType,
                            companyId,
                            active: { $ne: false },
                            deleted: { $ne: true },
                        })
                        .sort({ order: 1 })
                        .select(
                            "description name order upsell taskArray replaceTask packagesId selectedGroups useMinPrice minPrice",
                        )
                        .exec(),
                    this.taskModel
                        .find({
                            companyId,
                            type: createProjectDto.projectType,
                            deleted: { $ne: true },
                        })
                        .exec(),
                    this.taxModel.findOne({
                        companyId,
                        _id: thisOpp?.taxJurisdiction,
                    }),
                ]);

            // const currDate = new Date().toISOString();

            //roof size detail to be calculated every time
            const roofData = projectTypes.typeReplacement
                ? createProjectDto?.customData?.reroofAreas
                : createProjectDto?.customData?.pitch;

            const avgPitch = averagePitch(createProjectDto) || 0;
            const pitchModValue = getPitchMod(projectTypes?.pitchMod, avgPitch);

            // updating plywoodRate & plywoodLaborRate based on pitch mod
            const plywoodRate = pitchModValue * variables.plywoodRate;
            const plywoodLaborRate = pitchModValue * variables.plywoodLaborRate;
            delete variables.plywoodLaborRate;
            delete variables.plywoodRate;

            const roofSizeDetail = await this.getRoofSizeDetail(
                roofData,
                projectTypes?.pitchMod,
                projectTypes.typeReplacement,
            );

            const tasks = this.loadProjectTasksArray(
                createProjectDto.projectInputs,
                typeTasks,
                roofSizeDetail,
            );

            // Go through all tasks, aggregate with materials and labor and create a price for each task
            const taskPrices = this.getProjectTaskPrices(tasks, materials, labor, variables, units);

            // creating & saving project in DB
            const projectId = await this.addProject(
                {
                    ...createProjectDto,
                },
                companyId,
                projectTypes.name,
                createProjectDto?.name,
            );

            // creating price for the project
            await this.createNewProjectPrice(
                {
                    projectId: projectId,
                    oppId: createProjectDto.oppId,
                    contactId: createProjectDto.contactId,
                    duration: thisOpp?.duration || 0,
                    state: thisOpp.state,
                    avgPitch,
                    projectType: {
                        id: projectTypes._id,
                        name: projectTypes.name,
                        markup: projectTypes.markup,
                        deposit: projectTypes.deposit,
                        downPmt: projectTypes.downPmt,
                        typeMinimum: projectTypes.typeMinimum,
                        typeReplacement: projectTypes.typeReplacement,
                        permitRequired: projectTypes.permitRequired,
                        asbTestRequired: projectTypes.asbTestRequired,
                        minTravelPpl: projectTypes.minTravelPpl,
                    },
                    tax: {
                        salesTax: tax?.rate ?? 0,
                        materialTax: tax?.salesTax ?? 0,
                        city: tax?.city,
                        state: tax?.state || thisOpp.state,
                    },
                    tasks: taskPrices,
                    variables: {
                        commission: projectTypes?.salesCommision?.commission,
                        plywoodRate,
                        plywoodLaborRate,
                        ...variables,
                    }, //commission is now stored in project type
                    createdBy: createProjectDto.createdBy,
                    packages,
                    options,
                },
                companyId,
                createProjectDto.name,
                true,
            );

            return new CreatedResponse({ message: "Project created successfully!", id: projectId });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves projects for a specific user and company with pagination support
     *@param {string} userId - The id of the user requesting the projects
     *@param {string} companyId - The id of the company the projects belong to
     *@param {boolean} deleted - Whether or not to include deleted projects in the result
     *@param {PaginationRequestDto} paginationRequestDto - Object containing pagination parameters
     *@returns {Promise<OkResponse>} - Object containing project data and success message
     *@throws {HttpException|InternalServerErrorException} - Throws HttpException or InternalServerErrorException
     */
    async getProject(
        userId: string,
        companyId: string,
        oppId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const project = await this.projectModel.aggregate([
                {
                    $match: {
                        oppId,
                        companyId,
                        deleted: deleted,
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        localField: "createdBy",
                        foreignField: "_id",
                        as: "member",
                    },
                },
                {
                    $lookup: {
                        from: "User",
                        localField: "member.user",
                        foreignField: "_id",
                        as: "user",
                    },
                },
                {
                    $project: {
                        member: 0,
                    },
                },
                {
                    $skip: offset,
                },
                {
                    $limit: limit,
                },
            ]);
            return new OkResponse({ project });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves Opp for a specific stages of operation and company with pagination support
     *@param {string} userId - The id of the user requesting the Opp
     *@param {string} companyId - The id of the company the Opp belong to
     *@param {PaginationRequestDto} paginationRequestDto - Object containing pagination parameters
     *@returns {Promise<OkResponse>} - Object containing project data and success message
     *@throws {HttpException|InternalServerErrorException} - Throws HttpException or InternalServerErrorException
     */
    async getOppForPO(userId: string, companyId: string, paginationRequestDto: PaginationRequestDto) {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setMonth(thirtyDaysAgo.getMonth() - 1);

            // const limit = paginationRequestDto.limit || 50;
            // const offset = limit * (paginationRequestDto.skip || 0);

            const variables = await this.companySettingModel.findOne({ companyId });
            const defaultPO = variables.defaultPO;

            const searchFilter = paginationRequestDto.search
                ? {
                      $or: [
                          { PO: { $regex: paginationRequestDto.search, $options: "i" } },
                          { num: { $regex: paginationRequestDto.search, $options: "i" } },
                          {
                              $expr: {
                                  $regexMatch: {
                                      input: { $concat: ["$PO", " ", "$num"] },
                                      regex: paginationRequestDto.search,
                                      options: "i",
                                  },
                              },
                          },
                          {
                              $expr: {
                                  $regexMatch: {
                                      input: { $concat: ["$PO", "-", "$num"] },
                                      regex: paginationRequestDto.search,
                                      options: "i",
                                  },
                              },
                          },
                      ],
                  }
                : {};

            const readyOpps: any = await this.opportunityModel
                .find({
                    companyId,
                    deleted: false,
                    saleDate: { $exists: true },
                    acceptedProjectId: { $exists: true },
                    status: OpportunityStatusEnum.Active,
                    $or: [
                        {
                            jobCompletedDate: { $exists: true, $gte: thirtyDaysAgo },
                        },
                        {
                            jobCompletedDate: { $exists: false },
                        },
                    ],
                    ...searchFilter,
                })
                .sort({ PO: 1 })
                .select("_id PO num oppLat oppLong");
            readyOpps.unshift({
                _id: "companyDefaultPO",
                PO: defaultPO,
            });
            return new OkResponse({ readyOpps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves Opp for Clock in page with pitch & layers for mobile app
     *@param {string} userId - The id of the user requesting the Opp
     *@param {string} companyId - The id of the company the Opp belong to
     *@param {PaginationRequestDto} paginationRequestDto - Object containing pagination parameters
     *@returns {Promise<OkResponse>} - Object containing project data and success message
     *@throws {HttpException|InternalServerErrorException} - Throws HttpException or InternalServerErrorException
     */
    async getOppForPOForMobile(
        userId: string,
        companyId: string,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setMonth(thirtyDaysAgo.getMonth() - 1);

            // const limit = paginationRequestDto.limit || 50;
            // const offset = limit * (paginationRequestDto.skip || 0);

            const variables = await this.companySettingModel.findOne({ companyId });
            const defaultPO = variables.defaultPO;

            const searchFilter = paginationRequestDto.search
                ? {
                      $or: [
                          { PO: { $regex: paginationRequestDto.search, $options: "i" } },
                          { num: { $regex: paginationRequestDto.search, $options: "i" } },
                          {
                              $expr: {
                                  $regexMatch: {
                                      input: { $concat: ["$PO", " ", "$num"] },
                                      regex: paginationRequestDto.search,
                                      options: "i",
                                  },
                              },
                          },
                          {
                              $expr: {
                                  $regexMatch: {
                                      input: { $concat: ["$PO", "-", "$num"] },
                                      regex: paginationRequestDto.search,
                                      options: "i",
                                  },
                              },
                          },
                      ],
                  }
                : {};

            const readyOpps: any = await this.opportunityModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: false,
                        saleDate: { $exists: true },
                        acceptedProjectId: { $exists: true },
                        status: OpportunityStatusEnum.Active,
                        $or: [
                            {
                                jobCompletedDate: { $exists: true, $gte: thirtyDaysAgo },
                            },
                            {
                                jobCompletedDate: { $exists: false },
                            },
                        ],
                        ...searchFilter,
                    },
                },
                { $sort: { PO: 1 } },
                {
                    $lookup: {
                        from: "Project",
                        pipeline: [
                            {
                                $project: {
                                    pitches: "$customData.reroofAreas.pitch",
                                    layers: "$customData.reroofAreas.layers",
                                },
                            },
                        ],
                        foreignField: "_id",
                        localField: "acceptedProjectId",
                        as: "project",
                    },
                },
                {
                    $unwind: {
                        path: "$project",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "ProjectType",
                        pipeline: [
                            {
                                $project: {
                                    typeReplacement: 1,
                                },
                            },
                        ],
                        foreignField: "_id",
                        localField: "acceptedType",
                        as: "projectType",
                    },
                },
                {
                    $unwind: {
                        path: "$projectType",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        PO: 1,
                        num: 1,
                        oppLat: 1,
                        oppLong: 1,
                        projectTypeId: "$projectType._id",
                        typeReplacement: "$projectType.typeReplacement",
                        pitches: {
                            $concatArrays: [
                                { $ifNull: ["$project.pitches", []] },
                                { $ifNull: ["$modPieceWork.pitches", []] },
                            ],
                        },
                        layers: {
                            $concatArrays: [
                                { $ifNull: ["$project.layers", []] },
                                { $ifNull: ["$modPieceWork.layers", []] },
                            ],
                        },
                    },
                },
                {
                    $unset: "project",
                },
            ]);

            readyOpps.unshift({
                _id: "companyDefaultPO",
                PO: defaultPO,
                pitches: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                layers: [1, 2, 3, 4],
            });
            return new OkResponse({ readyOpps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves Opp for Clock in page with pitch & layers for pro users using custom projects
     *@param {string} userId - The id of the user requesting the Opp
     *@param {string} companyId - The id of the company the Opp belong to
     *@param {PaginationRequestDto} paginationRequestDto - Object containing pagination parameters
     *@returns {Promise<OkResponse>} - Object containing project data and success message
     *@throws {HttpException|InternalServerErrorException} - Throws HttpException or InternalServerErrorException
     */
    async getOppForCustomProjects(
        userId: string,
        companyId: string,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            // const limit = paginationRequestDto.limit || 50;
            // const offset = limit * (paginationRequestDto.skip || 0);

            const variables = await this.companySettingModel.findOne({ companyId });
            const defaultPO = variables.defaultPO;

            const searchFilter = paginationRequestDto.search
                ? {
                      $or: [
                          { PO: { $regex: paginationRequestDto.search, $options: "i" } },
                          { num: { $regex: paginationRequestDto.search, $options: "i" } },
                          {
                              $expr: {
                                  $regexMatch: {
                                      input: { $concat: ["$PO", " ", "$num"] },
                                      regex: paginationRequestDto.search,
                                      options: "i",
                                  },
                              },
                          },
                          {
                              $expr: {
                                  $regexMatch: {
                                      input: { $concat: ["$PO", "-", "$num"] },
                                      regex: paginationRequestDto.search,
                                      options: "i",
                                  },
                              },
                          },
                      ],
                  }
                : {};

            const today = startOfDate(new Date());

            const dateFilter = {
                $or: [
                    { startDate: { $exists: false }, endDate: { $exists: false } },
                    { startDate: { $lte: today }, endDate: { $exists: false } },
                    { startDate: { $exists: false }, endDate: { $gte: today } },
                    { startDate: { $lte: today }, endDate: { $gte: today } },
                ],
            };

            // Combine both filters into one final filter for the query
            const finalFilter = {
                ...searchFilter,
                ...dateFilter,
            };

            // Fetch opportunities using the combined filter
            const readyOpps: any = await this.customProjectService.getOppList(companyId, finalFilter);

            readyOpps.unshift({
                _id: "companyDefaultPO",
                PO: defaultPO,
                pitches: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                layers: [1, 2, 3, 4],
            });
            return new OkResponse({ readyOpps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async activeSearchForCustomProject(companyId: string, search: string) {
        try {
            const searchFilter = search
                ? {
                      $or: [
                          { PO: { $regex: search, $options: "i" } },
                          { num: { $regex: search, $options: "i" } },
                          { clientName: { $regex: search, $options: "i" } },
                          { address: { $regex: search, $options: "i" } },
                          {
                              $expr: {
                                  $regexMatch: {
                                      input: { $concat: ["$PO", " ", "$num"] },
                                      regex: search,
                                      options: "i",
                                  },
                              },
                          },
                          {
                              $expr: {
                                  $regexMatch: {
                                      input: { $concat: ["$PO", "-", "$num"] },
                                      regex: search,
                                      options: "i",
                                  },
                              },
                          },
                      ],
                  }
                : {};

            let opps = await this.customProjectService.getOppList(companyId, searchFilter);

            opps = opps.map((oppData) => {
                // Ensure clientName is defined and split safely
                const clientName = oppData?.clientName || ""; // Fallback to empty string if undefined
                const [firstName = "", lastName = ""] = clientName.split(" ");

                return {
                    opp: {
                        ...oppData,
                        client: {
                            firstName,
                            lastName,
                        },
                    },
                    oppId: oppData?._id,
                    projectId: oppData?._id,
                };
            });

            // Sort by endDate first in descending order, then startDate, handling null dates
            opps.sort((a, b) => {
                const endDateA = a.opp?.endDate ? new Date(a.opp.endDate) : null;
                const endDateB = b.opp?.endDate ? new Date(b.opp.endDate) : null;
                const startDateA = a.opp?.startDate ? new Date(a.opp.startDate) : null;
                const startDateB = b.opp?.startDate ? new Date(b.opp.startDate) : null;

                // Sort by endDate first, with nulls last, in descending order
                if (endDateA && endDateB) {
                    if (endDateA.getTime() !== endDateB.getTime()) {
                        return endDateB.getTime() - endDateA.getTime(); // Reverse the order for descending
                    }
                } else if (endDateA) {
                    return 1; // `b` comes first if only `a` has a valid endDate (reverse for descending)
                } else if (endDateB) {
                    return -1; // `a` comes first if only `b` has a valid endDate (reverse for descending)
                }

                // If endDates are equal or both null, sort by startDate in descending order
                if (startDateA && startDateB) {
                    return startDateB.getTime() - startDateA.getTime(); // Reverse the order for descending
                } else if (startDateA) {
                    return 1; // `b` comes first if only `a` has a valid startDate (reverse for descending)
                } else if (startDateB) {
                    return -1; // `a` comes first if only `b` has a valid startDate (reverse for descending)
                }

                return 0; // If both dates are null or equal, they are considered equal
            });
            return new OkResponse({ opps });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves a project by ID for a specific user and company, as long as it has not been deleted.
     *@param {string} userId - The ID of the user requesting the project.
     *@param {string} companyId - The ID of the company the project belongs to.
     *@param {string} projectId - The ID of the project to retrieve.
     *@param {boolean} deleted - Indicates if the project has been marked as deleted or not.
     *@returns {Promise<OkResponse>} - An object containing the project retrieved from the database, wrapped in an OkResponse object.
     *@throws {HttpException} - If there is an error with the HTTP request.
     *@throws {InternalServerErrorException} - If there is an internal server error when attempting to retrieve the project.
     */
    async getProjectById(userId: string, companyId: string, projectId: string, deleted: boolean) {
        try {
            const project = await this.projectModel.findOne({
                _id: projectId,
                companyId,
                deleted,
            });
            return new OkResponse({ project });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Deletes a project with the given ID.
     *@param {string} memberId - The ID of the user who wants to delete the project.
     *@param {DeleteProjectDto} deleteProjectDto - The DTO containing the ID of the project to be deleted.
     *@returns {Promise<NoContentResponse>} A promise that resolves with a NoContentResponse if the deletion was successful.
     *@throws {HttpException} If an HttpException is thrown while trying to delete the project.
     *@throws {InternalServerErrorException} If an internal server error occurs while trying to delete the project.
     */
    async deleteProject(memberId: string, companyId: string, deleteProjectDto: DeleteProjectDto) {
        try {
            const { name, oppId, deleted } = await this.projectModel
                .findOneAndUpdate(
                    { _id: deleteProjectDto.id, companyId, deleted: false },
                    {
                        $set: { deleted: true },
                    },
                    { new: true },
                )
                .select("name oppId deleted");

            if (!deleted) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            await this.opportunityService.updateOpportunityActivity(companyId, {
                body: `deleted ${name} Project`,
                currDate: new Date(),
                id: oppId,
                memberId,
            });

            return new NoContentResponse({ message: "Project deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Deletes a project permanently from the database.
     *@param {string} userId - The ID of the user making the request.
     *@param {DeleteProjectDto} deleteProjectDto - The DTO containing the ID and companyId of the project to be deleted.
     *@returns {Promise<NoContentResponse>} - A NoContentResponse object indicating that the project was deleted permanently.
     *@throws {HttpException} - If the request fails due to a client-side error.
     *@throws {InternalServerErrorException} - If the request fails due to a server-side error.
     */
    async permDeleteProject(companyId: string, deleteProjectDto: DeleteProjectDto) {
        let session;
        try {
            session = await this.connection.startSession();
            session.startTransaction();

            await this.priceModel.deleteMany(
                {
                    projectId: deleteProjectDto.id,
                    companyId,
                },
                { session },
            );

            const res = await this.projectModel.deleteOne(
                {
                    _id: deleteProjectDto.id,
                    companyId,
                },
                { session },
            );

            if (res.deletedCount > 0) {
                await session.commitTransaction();
                session.endSession();
                return new NoContentResponse({ message: "Project deleted permanently!" });
            } else throw new BadRequestException({ message: "Failed to Delete Project!" });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Restores a deleted Project.
     *@param memberId The ID of the user performing the operation.
     *@param restoreProjectDto The DTO containing the ID of the project to be restored.
     *@returns An OkResponse with a success message.
     *@throws An HttpException if the project with the specified ID is not found or if an internal server error occurs.
     */
    async restoreProject(memberId: string, companyId: string, restoreProjectDto: RestoreProjectDto) {
        try {
            const { deleted, name, oppId } = await this.projectModel
                .findOneAndUpdate(
                    { _id: restoreProjectDto.id, deleted: true },
                    {
                        $set: { deleted: false },
                    },
                    { new: true },
                )
                .select("name oppId deleted");

            if (deleted) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            await this.opportunityService.updateOpportunityActivity(companyId, {
                body: `restored ${name} Project`,
                currDate: new Date(),
                id: oppId,
                memberId,
            });

            return new OkResponse({ message: "Project restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createPackage(companyId: string, createPackageDto: CreatePackageDto) {
        try {
            const packageD = await this.packageModel
                .exists({
                    name: createPackageDto.name,
                    companyId,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (packageD) throw new HttpException("Package already exists", HttpStatus.BAD_REQUEST);

            const createdPackage = new this.packageModel({
                companyId,
                ...createPackageDto,
            });
            await createdPackage.save();
            return new CreatedResponse({ message: "Package created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deletePackage(userId: string, deletePackageDto: DeletePackageDto) {
        try {
            const result = await this.packageModel.updateOne(
                { _id: deletePackageDto.id },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Package deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeletePackage(companyId: string, deletePackageDto: DeletePackageDto) {
        try {
            await this.packageModel.deleteOne({
                _id: deletePackageDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "Package deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restorePackage(userId: string, restorePackageDto: RestorePackageDto) {
        try {
            const result = await this.packageModel.updateOne(
                { _id: restorePackageDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Package restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updatePackage(userId: string, updatePackageDto: UpdatePackageDto) {
        try {
            // updatePackageDto.taskArray = [...new Set(updatePackageDto.taskArray)];

            const result = await this.packageModel.updateOne(
                { _id: updatePackageDto.packageId, deleted: false },
                {
                    $set: { ...updatePackageDto },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Package updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPackage(
        userId: string,
        companyId: string,
        deleted: boolean,
        getPackageDto: GetPackageOptionDto,
    ) {
        try {
            const limit = getPackageDto.limit || 10;
            const offset = limit * (getPackageDto.skip || 0);
            const query = { companyId, deleted };

            const types = getPackageDto?.type?.split(",");
            if (types?.length) {
                query["type"] = { $in: types };
            }

            const packageDetail = await this.packageModel
                .find(query)
                .sort({ order: 1 })
                .skip(offset)
                .limit(limit);
            return new OkResponse({ packageDetail });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPackageById(userId: string, companyId: string, packageId: string, deleted: boolean) {
        try {
            const packageDetail = await this.packageModel.findOne({
                _id: packageId,
                companyId,
                deleted,
            });
            return new OkResponse({ packageDetail });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createOption(companyId: string, createOptionDto: CreateOptionDto) {
        try {
            const option = await this.optionModel
                .findOne({
                    name: createOptionDto.name,
                    companyId,
                    deleted: false,
                })
                .collation({ locale: "en", strength: 2 })
                .exec();
            if (option) throw new HttpException("Option already exists", HttpStatus.CONFLICT);

            const createdOption = new this.optionModel({
                companyId,
                ...createOptionDto,
            });
            await createdOption.save();
            return new CreatedResponse({ message: "Option created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateOption(userId: string, updateOptionDto: UpdateOptionDto) {
        try {
            const result = await this.optionModel.updateOne(
                { _id: updateOptionDto.optionId, deleted: false },
                {
                    $set: { ...updateOptionDto },
                },
            );

            if (result.modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Option updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteOption(userId: string, deleteOptionDto: DeleteRestoreDto) {
        try {
            const result = await this.optionModel.updateOne(
                { _id: deleteOptionDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Option deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreOption(userId: string, restoreOptionDto: DeleteRestoreDto) {
        try {
            const result = await this.optionModel.updateOne(
                { _id: restoreOptionDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Option restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getOption(userId: string, companyId: string, deleted: boolean, getOptionDto: GetPackageOptionDto) {
        try {
            const limit = getOptionDto.limit || 10;
            const offset = limit * (getOptionDto.skip || 0);
            const query = { companyId, deleted };

            const types = getOptionDto?.type?.split(",");
            if (types?.length) {
                query["type"] = { $in: types };
            }

            const options = await this.optionModel.find(query).sort({ order: 1 }).skip(offset).limit(limit);
            return new OkResponse({ options });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getOptionById(userId: string, companyId: string, OptionId: string, deleted: boolean) {
        try {
            const option = await this.optionModel.findOne({
                _id: OptionId,
                companyId,
                deleted,
            });
            return new OkResponse({ option });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultPackages(companyId: string, createdBy: string, tasksUUID, projectTypeUUID, packagesUUID) {
        try {
            const defaultValues = defaultPackages(tasksUUID, projectTypeUUID, packagesUUID);
            const data = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));
            await this.packageModel.insertMany(data);
            return new CreatedResponse({
                message: "Default Packages created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultOptions(
        companyId: string,
        createdBy: string,
        tasksUUID,
        projectTypeUUID,
        packagesUUID,
        taskGroup,
    ) {
        try {
            const defaultValues = defaultOptions(tasksUUID, projectTypeUUID, packagesUUID, taskGroup);
            const data = defaultValues.map((value) => ({
                ...value,
                companyId,
                createdBy,
                deleted: false,
            }));
            await this.optionModel.insertMany(data);
            return new CreatedResponse({
                message: "Default Options created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createOrder(companyId: string, createOrderDto: CreateOrderDto) {
        try {
            const { oppId, isUpdate, projectId, projectPriceId } = createOrderDto;
            const [orderExists, oppExists, projectExists, priceExists] = await Promise.all([
                this.orderModel.exists({ companyId, oppId, deleted: false }),
                this.opportunityModel.exists({ _id: oppId, deleted: false }),
                this.projectModel.exists({ _id: projectId, deleted: false }),
                this.priceModel.exists({ _id: projectPriceId, oppId, deleted: false }),
            ]);

            if (!isUpdate && orderExists) {
                throw new BadRequestException("There is already an accepted Contract");
            }
            if (!oppExists) throw new BadRequestException("Opportunity does not exist");
            if (!projectExists) throw new BadRequestException("Project does not exist");
            if (!priceExists) throw new BadRequestException("Price does not exist");

            delete createOrderDto.isUpdate;

            // update profit score
            const { jobTotal, mTotal, lTotal, commission } = createOrderDto.priceTotals;
            const budgetScore = profitScoreCalc(jobTotal, mTotal, lTotal, commission, 0);

            const createdOrder = await this.orderModel.findOneAndUpdate(
                {
                    oppId,
                    companyId,
                    deleted: false,
                },
                {
                    companyId,
                    ...createOrderDto,
                },
                { new: true, upsert: true },
            );
            await this.opportunityModel.updateOne({ _id: oppId }, { $set: { budgetScore } });

            return createdOrder._id;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async setAccepted(
        userId: string,
        companyId: string,
        memberId: string,
        updateOppClientDto: UpdateOppClientDto,
    ) {
        let session;
        try {
            session = await this.connection.startSession();
            session.startTransaction();

            const opp = await this.opportunityModel.findOne({
                _id: updateOppClientDto.oppId,
                companyId,
            });

            const result3 = await this.projectModel.findOneAndUpdate(
                {
                    _id: updateOppClientDto.projectId,
                    companyId,
                },
                {
                    orderId: updateOppClientDto.orderId,
                },
                { session },
            );

            const pType = await this.projectTypeModel.findOne({ _id: result3.projectType });

            // to calc commission
            let totalCommission = 0;
            if (opp?.salesPerson)
                totalCommission = await this.opportunityService.calcSalesPersonCommission(
                    companyId,
                    opp.salesPerson,
                    result3.projectType,
                    updateOppClientDto.soldValue,
                    new Date(),
                    opp.selfGen,
                );

            const result = await this.opportunityModel.updateOne(
                {
                    _id: updateOppClientDto.oppId,
                    companyId,
                },
                {
                    $set: {
                        acceptedProjectId: updateOppClientDto.projectId,
                        orderId: updateOppClientDto.orderId,
                        soldValue: updateOppClientDto.soldValue,
                        realRevValue: updateOppClientDto.realRevValue,
                        discount: updateOppClientDto.discount ?? 0,
                        acceptedType: result3.projectType,
                        paymentType: updateOppClientDto?.paymentType,
                        salesCommission: {
                            total: totalCommission,
                            sale: roundTo2(totalCommission * pType?.salesCommision?.jobSale),
                            start: roundTo2(totalCommission * pType?.salesCommision?.jobStart),
                            completed: roundTo2(
                                totalCommission -
                                    roundTo2(totalCommission * pType?.salesCommision?.jobSale) -
                                    roundTo2(totalCommission * pType?.salesCommision?.jobStart),
                            ),
                            modificationAmount: 0,
                        },
                    },
                },
                { session },
            );

            // update contact
            const result2 = await this.contactModel.updateOne(
                {
                    _id: updateOppClientDto.contactId,
                    companyId,
                },
                {
                    $set: {
                        workType: result3.projectType,
                        type: ContactTypeEnum.CLIENT,
                    },
                    $push: {
                        orders: {
                            orderId: updateOppClientDto.orderId,
                            orderValue: updateOppClientDto.soldValue,
                        },
                    },
                },
                { session },
            );

            await session.commitTransaction();
            session.endSession();

            if (result2.modifiedCount === 0 || result.modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
                //TODO: add event here
            }

            return new OkResponse({ message: "Project accepted successfully!" });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Update a Order with the given data
     *@param userId - The ID of the user making the request
     *@param updateOrderDto - The data to update the Order with
     *@returns A success response with a message indicating the Order was updated successfully
     *@throws HttpException if the Order does not exist or if there is an internal server error
     */
    async updateOrder(companyId: string, updateOrderDto: UpdateOrderDto) {
        try {
            const { actualTotals, orderId, colors, notes, projectId, originalMatList, modifiedBudget } =
                updateOrderDto;

            const orderData = await this.orderModel.findOne({ _id: orderId });

            if (!orderData) {
                throw new NotFoundException("Order does not exist");
            }
            // Update the projects array with new colors and notes for the specified projectId
            const updatedProjects = orderData.projects.map((project) => {
                if (projectId === project.projectId) {
                    const updatedData = {
                        ...project,
                        ...(originalMatList !== undefined && { originalMatList }),
                    };
                    if (colors) updatedData.colors = colors;
                    if (notes) updatedData.notes = notes;

                    if (originalMatList !== undefined) {
                        updatedData.originalMatList = originalMatList;
                    }
                    return updatedData;
                }
                return project;
            });

            const updatedProject = await this.projectModel.updateOne(
                { _id: projectId, companyId, deleted: false },
                { $set: { originalMatList } },
            );

            // Create the update object with only defined fields
            let updateData: any = {
                ...(actualTotals !== undefined && { actualTotals }),
                projects: updatedProjects,
            };

            if (modifiedBudget?.subContractorTotal === 0) {
                updateData = {
                    ...updateData,
                    $unset: { modifiedBudget: "" },
                };
            } else {
                updateData = {
                    ...updateData,
                    modifiedBudget,
                };
            }

            // Perform the update operation
            const result = await this.orderModel.updateOne(
                { _id: orderId, companyId, deleted: false },
                { ...(updateData.$unset ? updateData : { $set: updateData }) },
            );
            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Order updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Delete a Order permanently.
     *@param userId The ID of the user making the request.
     *@param deleteOrderDto The DTO containing the ID of the Order to be deleted and the ID of the company it belongs to.
     *@returns A NoContentResponse with a success message if the Order was deleted successfully.
     *@throws HttpException with a message "Order not found" if the Order with the given ID and company ID was not found.
     *@throws InternalServerErrorException with the error message if any other error occurs during the deletion process.
     */
    async deleteOrder(companyId: string, deleteOrderDto: DeleteOrderDto) {
        let session;
        try {
            session = await this.connection.startSession();
            session.startTransaction();

            const result = await this.orderModel.deleteOne(
                {
                    _id: deleteOrderDto.id,
                    companyId,
                },
                { session },
            );
            // removing orderId from opp
            const opp = await this.opportunityModel.findOneAndUpdate(
                {
                    orderId: deleteOrderDto.id,
                },
                {
                    $unset: {
                        acceptedProjectId: 1,
                        orderId: 1,
                        soldValue: 1,
                        realRevValue: 1,
                        discount: 1,
                        acceptedType: 1,
                        salesCommission: 1,
                        paymentType: 1,
                    },
                },
                { new: true, session },
            );
            // removing orderId from project
            await this.projectModel.updateOne(
                {
                    orderId: deleteOrderDto.id,
                },
                {
                    $unset: { orderId: 1 },
                },
                { session },
            );

            //checking for active orders
            //TODO: new logic needed
            // const activeOrder: any = await this.clientModel
            //     .findOne({
            //         _id: opp.clientId,
            //     })
            //     .select("orders");

            // const orderArr = [];
            // activeOrder?.orders?.map((o) => {
            //     if (Object.keys(o)?.length) orderArr.push(o);
            // });
            // const status = orderArr?.length === 1 ? ClientStatusEnum.PROSPECT : ClientStatusEnum.CLIENT;

            // removing orderId from client
            await this.contactModel.updateOne(
                {
                    _id: opp.contactId,
                    companyId,
                },
                {
                    $unset: { "orders?.$[elem]?.orderId": 1, "orders?.$[elem]?.orderValue": 1 },
                    // $set: { status },
                },
                {
                    arrayFilters: [{ "elem?.orderId": deleteOrderDto.id }],
                    session,
                },
            );

            if (result.deletedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }

            await session.commitTransaction();
            session.endSession();

            return new NoContentResponse({ message: "Order deleted successfully!" });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // /**
    //  *Deletes a Order from the database.
    //  *@param userId - The ID of the user making the request.
    //  *@param deleteOrderDto - The data transfer object containing the ID of the Order to be deleted.
    //  *@throws {HttpException} - Throws an HTTP exception if the Order does not exist or if there is a server error.
    //  *@throws {InternalServerErrorException} - Throws an internal server error exception if there is an unexpected error.
    //  *@returns {NoContentResponse} - Returns a response indicating that the Order was deleted successfully.
    //  */
    // async permDeleteOrder(companyId: string, deleteOrderDto: DeleteOrderDto) {
    //     try {
    //         // const res = await this.deleteOrder(companyId, deleteOrderDto);

    //         // if (res.statusCode !== 204)
    //         //     throw new BadRequestException({ message: "Failed to update changes!" });

    //         await this.orderModel.deleteOne({
    //             _id: deleteOrderDto.id,
    //             companyId,
    //         });
    //         return new NoContentResponse({ message: "Order deleted permanently!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    /**
     *Restores a deleted Order .
     *@param userId The ID of the user performing the operation.
     *@param restoreOrderDto The DTO containing the ID of the Order to be restored.
     *@returns An OkResponse with a success message.
     *@throws An HttpException if the Order with the specified ID is not found or if an internal server error occurs.
     */
    async restoreOrder(userId: string, restoreOrderDto: RestoreOrderDto) {
        try {
            const result = await this.orderModel.updateOne(
                { _id: restoreOrderDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Project Type restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     *Retrieves order for a given company ID, including deleted records if requested.
     *@param userId - The ID of the user making the request.
     *@param companyId - The ID of the company for which to retrieve order.
     *@param deleted - Whether to include deleted order in the results.
     *@param paginationRequestDto - The pagination options for the query.
     *@returns A response containing an array of order.
     *@throws {HttpException} When a client error occurs (e.g. invalid input parameters).
     *@throws {InternalServerErrorException} When a server error occurs.
     */
    async getOrder(companyId: string, getOrderDto: GetOrderDto) {
        try {
            const { deleted, oppId } = getOrderDto;
            const limit = getOrderDto.limit || 10;
            const offset = limit * (getOrderDto.skip || 0);
            const query = {
                companyId,
                deleted,
                ...(oppId && { oppId }),
            };
            const Order = await this.orderModel.find(query).skip(offset).limit(limit);
            Order.forEach((order) => {
                order.projects.forEach((project) => {
                    const laborTime = this.totalLaborTime(project);
                    project["laborTime"] = laborTime;
                });
            });
            return new OkResponse({ Order });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    totalLaborTime(project: any) {
        // const order = Orders.findOne(Router.current().params.orderId);
        const workOrder = project.workOrder;
        let hours = 0;
        for (let i = 0; i < workOrder.length; i++) {
            hours += workOrder[i].ttlHours;
        }
        return roundTo2(hours);
    }

    /**
     *Retrieves the order by its ID.
     *@param {string} userId - The ID of the user.
     *@param {string} companyId - The ID of the company.
     *@param {string} orderId - The ID of the order.
     *@param {boolean} deleted - A flag indicating whether the order is deleted or not.
     *@returns {Promise<OkResponse>} An object containing the retrieved order.
     *@throws {HttpException} If an HTTP exception occurs.
     *@throws {InternalServerErrorException} If an internal server error occurs.
     */
    async getOrderById(userId: string, companyId: string, OrderId: string, deleted: boolean) {
        try {
            const Order = await this.orderModel.findOne({
                _id: OrderId,
                companyId,
                deleted,
            });

            if (!Order) throw new NotFoundException("Order not found");

            Order.projects.forEach((project) => {
                const laborTime = this.totalLaborTime(project);
                project["laborTime"] = laborTime;
            });

            return new OkResponse({ Order });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addPrice(companyId: string, addPriceDto: AddPriceDto) {
        try {
            const { createdBy, projectId, discount } = addPriceDto;
            const project: any = await this.projectModel
                .findOne({
                    _id: projectId,
                    companyId,
                    deleted: false,
                })
                .populate("projectType", null, "ProjectType")
                .populate("oppId", null, "Opportunity")
                .exec();

            const projectTypes: any = project.projectType;
            const opp: any = project.oppId;
            const contactId: any = project.contactId;

            const [variables, typeTasks, packages, options, materials, labor, units, tax] = await Promise.all(
                [
                    this.companySettingModel
                        .findOne({ companyId })
                        .select(
                            "ttlBurden matMarkup modOH modP modS financeMod laborWaste travelHrlyRate manHourRate plywoodRate plywoodLaborRate",
                        )
                        .lean(),

                    this.taskModel
                        .find({
                            companyId,
                            type: projectTypes._id,
                            deleted: { $ne: true },
                        })
                        .exec(),
                    this.packageModel
                        .find({
                            type: projectTypes._id,
                            companyId,
                            deleted: { $ne: true },
                            active: { $ne: false },
                        })
                        .sort({ order: 1 })
                        .select("description name order upsell taskArray")
                        .exec(),
                    this.optionModel
                        .find({
                            type: projectTypes._id,
                            companyId,
                            active: { $ne: false },
                            deleted: { $ne: true },
                        })
                        .sort({ order: 1 })
                        .select(
                            "description name order upsell taskArray replaceTask packagesId selectedGroups useMinPrice minPrice",
                        )
                        .exec(),
                    this.materialModel.find({ companyId, deleted: { $ne: true } }).exec(),
                    // this.crewPositionModel.find({ companyId, deleted: { $ne: true } }).exec(),
                    this.workTaskModel.find({ companyId, delete: false }).exec(),
                    this.unitModel.find({ companyId, deleted: { $ne: true } }).exec(),
                    this.taxModel.findOne({
                        companyId,
                        _id: opp?.taxJurisdiction,
                    }),
                ],
            );
            // const currDate = new Date().toString();

            const roofData = projectTypes.typeReplacement
                ? project?.customData?.reroofAreas
                : project?.customData?.pitch;

            const avgPitch = averagePitch(project) || 0;
            const pitchModValue = getPitchMod(projectTypes?.pitchMod, avgPitch);

            // updating plywoodRate & plywoodLaborRate based on pitch mod
            const plywoodRate = pitchModValue * variables.plywoodRate;
            const plywoodLaborRate = pitchModValue * variables.plywoodLaborRate;
            delete variables.plywoodLaborRate;
            delete variables.plywoodRate;

            const roofSizeDetail = await this.getRoofSizeDetail(
                roofData,
                projectTypes?.pitchMod,
                projectTypes.typeReplacement,
            );

            const projectTasks = this.loadProjectTasksArray(project.projectInputs, typeTasks, roofSizeDetail);
            const tasks = this.getProjectTaskPrices(projectTasks, materials, labor, variables, units);

            const result = await this.createNewProjectPrice(
                {
                    projectId,
                    oppId: opp._id,
                    contactId,
                    duration: opp?.duration || 0,
                    state: opp.state,
                    avgPitch,
                    projectType: {
                        id: projectTypes._id,
                        name: projectTypes.name,
                        markup: projectTypes.markup,
                        deposit: projectTypes.deposit,
                        downPmt: projectTypes.downPmt,
                        typeMinimum: projectTypes.typeMinimum,
                        typeReplacement: projectTypes.typeReplacement,
                        permitRequired: projectTypes.permitRequired,
                        asbTestRequired: projectTypes.asbTestRequired,
                        minTravelPpl: projectTypes.minTravelPpl,
                    },
                    tax: {
                        salesTax: tax?.rate ?? 0,
                        materialTax: tax?.salesTax ?? 0,
                        city: tax?.city,
                        state: tax?.state || opp.state,
                    },
                    tasks,
                    variables: {
                        commission: projectTypes?.salesCommision?.commission,
                        plywoodRate,
                        plywoodLaborRate,
                        ...variables,
                    }, //commission is now stored in project type
                    createdBy,
                    packages,
                    discount,
                    options,
                },
                companyId,
                project.name,
                false,
            );

            return new CreatedResponse({ message: "New Price created successfully!", _id: result });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createNewProjectPrice(
        createPriceDto: CreatePriceDto,
        companyId: string,
        projectName: string,
        newProject: boolean,
    ) {
        try {
            const createdPrice = new this.priceModel({
                companyId,
                ...createPriceDto,
                active: true,
            });

            await createdPrice.save();

            //creating opp activity
            // await this.crmService.updateOpportunityActivity(userId, {
            //     body: `New price added for ${projectName}`,
            //     currDate: new Date(),
            //     id: createPriceDto.oppId,
            //     memberId: createPriceDto.createdBy,
            // });

            await this.setPriceActive(companyId, createPriceDto.projectId, createdPrice._id, newProject);

            return createdPrice._id;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPrices(userId: string, companyId: string, projectId: string, deleted: boolean) {
        try {
            const prices = await this.priceModel.aggregate([
                {
                    $match: {
                        companyId,
                        projectId,
                        deleted,
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        localField: "createdBy",
                        foreignField: "_id",
                        as: "member",
                    },
                },
                {
                    $lookup: {
                        from: "User",
                        localField: "member.user",
                        foreignField: "_id",
                        as: "createdByUser",
                    },
                },
                {
                    $addFields: {
                        user: {
                            $arrayElemAt: ["$createdByUser", 0],
                        },
                    },
                },
                {
                    $project: {
                        createdByUser: 1,
                        createdAt: 1,
                        _id: 1,
                        companyId: 1,
                        projectId: 1,
                        active: 1,
                        oppId: 1,
                        createdBy: 1,
                        selectedPackages: 1,
                        discount: 1,
                    },
                },
                {
                    $sort: {
                        createdAt: -1,
                    },
                },
            ]);
            return new OkResponse({ prices });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getPriceById(userId: string, companyId: string, priceId: string) {
        try {
            const price = await this.priceModel.findOne({
                _id: priceId,
                companyId,
            });
            return new OkResponse({ price });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * @description Get prices by project type
     * @param companyId - The ID of the company to filter prices.
     * @param projectType - The type of the project to filter prices.
     * @param oppId - The ID of the opportunity to filter prices.
     * @returns The HTTP response containing the filtered prices.
     * @throws InternalServerErrorException if there is a server error.
     */
    async getPriceByProjectType(companyId: string, priceId: string, oppId: string): Promise<OkResponse> {
        try {
            const prices = await this.priceModel.aggregate([
                {
                    $match: {
                        companyId: companyId,
                        _id: { $ne: priceId },
                        oppId: oppId,
                        active: true,
                    },
                },
                {
                    $lookup: {
                        from: "Project",
                        localField: "projectId",
                        foreignField: "_id",
                        as: "projectDetails",
                    },
                },
                {
                    $unwind: "$projectDetails",
                },
                {
                    $match: {
                        "projectDetails.deleted": false,
                    },
                },
                {
                    $addFields: {
                        projectName: "$projectDetails.name",
                        projectNotes: "$projectDetails.notes",
                        projectCustomData: "$projectDetails.customData",
                    },
                },
                {
                    $project: {
                        projectDetails: 0,
                    },
                },
            ]);

            prices.forEach((price) => {
                const { projectName, projectNotes, projectCustomData, projectId: _id } = price;
                price.projectId = {
                    _id,
                    name: projectName,
                    notes: projectNotes,
                    customData: projectCustomData,
                };
                delete price.projectName;
                delete price.projectNotes;
                delete price.projectCustomData;
            });

            return new OkResponse({ prices });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async setPriceActive(companyId: string, projectId: string, priceId: string, newProject?: boolean) {
        try {
            const param = newProject
                ? {
                      active: false,
                      deleted: true,
                  }
                : { active: false };
            await this.priceModel.updateMany(
                { companyId, projectId, deleted: false, _id: { $ne: priceId } },
                { $set: param },
            );

            // permanently deleting in active prices
            if (newProject) {
                await this.priceModel.deleteMany({
                    companyId,
                    projectId,
                    deleted: true,
                    _id: { $ne: priceId },
                });
            }

            await this.priceModel.findByIdAndUpdate(priceId, { active: true });

            await this.setProjectPrice(projectId, priceId);

            return new OkResponse({ message: "New Price set to active" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async setProjectPrice(projectId: string, priceId: string) {
        try {
            await this.projectModel.findOneAndUpdate(
                { _id: projectId, deleted: false },
                {
                    $set: { priceId },
                },
                { new: true, upsert: true },
            );
            return new OkResponse({ message: "Price updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deletePrice(companyId: string, deletePriceDto: DeletePriceDto) {
        try {
            const result = await this.priceModel.updateOne(
                {
                    _id: deletePriceDto.id,
                    companyId,
                    deleted: false,
                    active: false,
                },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Price deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeletePrice(companyId: string, deletePriceDto: DeletePriceDto) {
        try {
            const res = await this.priceModel.deleteOne({
                _id: deletePriceDto.id,
                companyId,
                active: false,
            });
            if (res.deletedCount > 0) return new NoContentResponse({ message: "Price deleted permanently!" });
            else throw new BadRequestException({ message: "Failed to delete Price!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restorePrice(userId: string, restorePriceDto: RestorePriceDto) {
        try {
            const result = await this.priceModel.updateOne(
                { _id: restorePriceDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Price restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * get search list of projects using search for project report
     * @param userId - The ID of the user making the request.
     * @param companyId - The ID of the company making the request.
     * @param search - The text to search.
     * @returns - A promise that resolves to an HTTP response.
     */
    async activeSearch(search: string, companyId: string) {
        try {
            const matchStage: any = {
                deleted: false,
                jobStartedDate: { $exists: true },
            };

            if (search) {
                matchStage["$or"] = [
                    { street: { $regex: search, $options: "i" } },
                    { phone: { $regex: search, $options: "i" } },
                    { email: { $regex: search, $options: "i" } },
                    { PO: { $regex: search, $options: "i" } },
                    { num: { $regex: search, $options: "i" } },
                    {
                        $expr: {
                            $regexMatch: {
                                input: { $concat: ["$firstName", " ", "$lastName"] },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                    {
                        $expr: {
                            $regexMatch: {
                                input: { $concat: ["$PO", " ", "$num"] },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                    {
                        $expr: {
                            $regexMatch: {
                                input: { $concat: ["$PO", "-", "$num"] },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                ];
            }

            const opps1 = await this.orderModel.aggregate([
                {
                    $match: {
                        companyId: companyId,
                        deleted: { $ne: true },
                    },
                },
                {
                    $lookup: {
                        from: "Opportunity",
                        pipeline: [
                            {
                                $match: matchStage,
                            },
                            {
                                $lookup: {
                                    from: "Contact",
                                    foreignField: "_id",
                                    localField: "clientId",
                                    as: "client",
                                },
                            },
                            {
                                $unwind: {
                                    path: "$client",
                                    preserveNullAndEmptyArrays: false,
                                },
                            },
                            {
                                $project: {
                                    PO: 1,
                                    num: 1,
                                    street: 1,
                                    orderId: 1,
                                    client: 1,
                                    jobStartedDate: 1,
                                    jobCompletedDate: 1,
                                    phone: "$client.phone",
                                    email: "$client.email",
                                    firstName: "$client.firstName",
                                    lastName: "$client.lastName",
                                },
                            },
                        ],
                        foreignField: "_id",
                        localField: "oppId",
                        as: "opp",
                    },
                },
                {
                    $unwind: {
                        path: "$opp",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $sort: { "opp.jobStartedDate": -1 },
                },
                {
                    $limit: 20,
                },
                {
                    $project: {
                        _id: 1,
                        companyId: 1,
                        projectId: 1,
                        oppId: 1,
                        opp: {
                            $mergeObjects: [
                                "$opp",
                                {
                                    startDate: "$opp.jobStartedDate",
                                    endDate: "$opp.jobCompletedDate",
                                }, // Rename field here
                            ],
                        },
                    },
                },
            ]);

            const ids = opps1.map((o) => o._id);

            const opps2 = await this.orderModel.aggregate([
                {
                    $match: {
                        companyId: companyId,
                        deleted: { $ne: true },
                        _id: { $nin: ids },
                    },
                },
                {
                    $lookup: {
                        from: "Opportunity",
                        pipeline: [
                            {
                                $match: { deleted: { $ne: true }, jobStartedDate: { $exists: true } },
                            },
                            {
                                $lookup: {
                                    from: "Contact",
                                    pipeline: [
                                        {
                                            $match: {
                                                $or: [
                                                    { firstName: { $regex: search, $options: "i" } },
                                                    { lastName: { $regex: search, $options: "i" } },
                                                    { phone: { $regex: search, $options: "i" } },
                                                    { email: { $regex: search, $options: "i" } },
                                                    { street: { $regex: search, $options: "i" } },
                                                    {
                                                        "contacts.firstName": {
                                                            $regex: search,
                                                            $options: "i",
                                                        },
                                                    },
                                                    {
                                                        "contacts.lastName": {
                                                            $regex: search,
                                                            $options: "i",
                                                        },
                                                    },
                                                    { "contacts.phone": { $regex: search, $options: "i" } },
                                                    { "contacts.email": { $regex: search, $options: "i" } },
                                                    // { "contacts.notes": { $regex: term, $options: "i" } },
                                                    {
                                                        $expr: {
                                                            $regexMatch: {
                                                                input: {
                                                                    $concat: ["$firstName", " ", "$lastName"],
                                                                },
                                                                regex: search,
                                                                options: "i",
                                                            },
                                                        },
                                                    },
                                                    {
                                                        $expr: {
                                                            $regexMatch: {
                                                                input: {
                                                                    $reduce: {
                                                                        input: {
                                                                            $map: {
                                                                                input: "$contacts",
                                                                                as: "contact",
                                                                                in: {
                                                                                    $concat: [
                                                                                        "$$contact.firstName",
                                                                                        " ",
                                                                                        "$$contact.lastName",
                                                                                    ],
                                                                                },
                                                                            },
                                                                        },
                                                                        initialValue: "",
                                                                        in: {
                                                                            $cond: [
                                                                                { $eq: ["$$value", ""] },
                                                                                "$$this",
                                                                                {
                                                                                    $concat: [
                                                                                        "$$value",
                                                                                        " ",
                                                                                        "$$this",
                                                                                    ],
                                                                                },
                                                                            ],
                                                                        },
                                                                    },
                                                                },
                                                                regex: search,
                                                                options: "i",
                                                            },
                                                        },
                                                    },
                                                ],
                                            },
                                        },
                                    ],
                                    foreignField: "_id",
                                    localField: "clientId",
                                    as: "client",
                                },
                            },
                            {
                                $unwind: {
                                    path: "$client",
                                    preserveNullAndEmptyArrays: false,
                                },
                            },
                            {
                                $project: {
                                    PO: 1,
                                    num: 1,
                                    street: 1,
                                    orderId: 1,
                                    client: 1,
                                    jobStartedDate: 1,
                                    jobCompletedDate: 1,
                                    phone: "$client.phone",
                                    email: "$client.email",
                                    firstName: "$client.firstName",
                                    lastName: "$client.lastName",
                                },
                            },
                        ],
                        foreignField: "_id",
                        localField: "oppId",
                        as: "opp",
                    },
                },
                {
                    $unwind: {
                        path: "$opp",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $sort: { "opp.jobStartedDate": -1 },
                },
                {
                    $limit: 20,
                },
                {
                    $project: {
                        _id: 1,
                        companyId: 1,
                        projectId: 1,
                        oppId: 1,
                        opp: {
                            $mergeObjects: [
                                "$opp",
                                {
                                    startDate: "$opp.jobStartedDate",
                                    endDate: "$opp.jobCompletedDate",
                                }, // Rename field here
                            ],
                        },
                    },
                },
            ]);

            const allOpps = [...opps1, ...opps2];

            const opps = allOpps
                .sort((a, b) => {
                    return b.opp.startDate.getTime() - a.opp.startDate.getTime();
                })
                .slice(0, 20);

            return new OkResponse({ opps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * update packageId selected for the order in price
     * @param priceId - The ID of the price to be updated.
     * @param selectedPackage - The Array of selected Id of packages.
     * @returns - A promise that resolves to an HTTP response.
     */
    async updatePrice(updatePriceDto: UpdatePriceDto) {
        try {
            const { priceId, discount, selectedPackage } = updatePriceDto;

            const result = await this.priceModel.updateOne(
                { _id: priceId, deleted: false },
                {
                    $set: { selectedPackages: selectedPackage, discount },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to update changes!" });
            }

            return new OkResponse({ message: "Price PackageId update successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async checkIfProjectAccepted(projectId: string) {
        try {
            const result = await this.orderModel.findOne({ projectId, deleted: false });

            if (result) return true;
            else return false;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async projectInputDetails(companyId: string, projectInputsDto: ProjectInputsDto) {
        try {
            const { projectType, projectId } = projectInputsDto;
            const inputs: any = await this.inputModel
                .find({
                    projectType,
                    companyId,
                    hidden: false,
                })
                .sort({ orderNumber: 1 });

            const tasks = await this.taskModel.find({
                type: projectType,
                companyId,
                active: { $ne: false },
            });

            // Fetch packages and options in parallel to improve performance
            const [packages, options] = await Promise.all([
                this.packageModel.find(
                    {
                        type: projectType,
                        companyId,
                        active: { $ne: false },
                    },
                    { taskArray: 1 },
                ),
                this.optionModel.find(
                    {
                        type: projectType,
                        companyId,
                        active: { $ne: false },
                    },
                    { taskArray: 1 },
                ),
            ]);

            // Extract and merge task arrays, then remove duplicates using Set
            const tasksUsedInPackagAndOptions = Array.from(
                new Set([
                    ...packages.flatMap((pkg) => pkg.taskArray),
                    ...options.flatMap((option) => option.taskArray),
                ]),
            );

            const inputArray = [];

            let proj;
            if (projectId) {
                proj = await this.projectModel.findOne({ _id: projectId, companyId });
            }

            inputs.map((input) => {
                const data = input;
                let inputFound = false;
                tasks.map((task) => {
                    task.input.map((ti) => {
                        if (data._id === ti.input && tasksUsedInPackagAndOptions.includes(task._id)) {
                            data.desc = task?.description;
                            inputFound = true;
                        }
                    });
                });

                if (projectId) {
                    for (let i = 0; i < proj?.projectInputs.length; i++) {
                        if (proj?.projectInputs[i]._id === data._id) {
                            data.value = proj?.projectInputs[i]?.value ?? 0;
                            // data.desc = task?.description;
                            inputFound = true;
                        }
                    }
                }
                // if input not in any active task dont show its
                if (!inputFound) return;
                inputArray.push(data);
            });

            return new OkResponse({ inputArray });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
